/* تصميم موحد ونظيف لجميع صفحات النظام */
/* بدون ألوان حمراء أو صفراء - بدون حركات - أداء عالي */

/* إعادة تعيين شاملة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    animation: none !important;
    transition: none !important;
}

/* الخطوط الواضحة */
body, html {
    font-family: 'Cairo', '<PERSON><PERSON><PERSON>', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
    background: #f8f9fa !important;
    color: #2d3748 !important;
    line-height: 1.6 !important;
    font-size: 16px !important;
}

/* رأس الصفحة الموحد - مضغوط */
.modern-header {
    background: white !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
    position: sticky !important;
    top: 0 !important;
    z-index: 1000 !important;
    width: 100% !important;
    min-height: auto !important;
    height: auto !important;
}

/* الشريط العلوي - مضغوط */
.top-bar {
    background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%) !important;
    color: white !important;
    padding: 5px 0 !important;
    font-size: 12px !important;
    min-height: 35px !important;
}

.header-info, .header-contact {
    display: flex !important;
    align-items: center !important;
    gap: 10px !important;
    color: white !important;
}

.header-info i {
    color: #bfdbfe !important;
}

.header-contact i {
    color: #a7f3d0 !important;
}

#current-time, #current-date {
    font-weight: 600 !important;
    color: white !important;
}

/* الشريط الرئيسي - مضغوط */
.main-navbar {
    background: white !important;
    padding: 8px 0 !important;
    border-bottom: 1px solid #e5e7eb !important;
    min-height: 60px !important;
}

/* الشعار - مضغوط */
.brand-container {
    display: flex !important;
    align-items: center !important;
    gap: 10px !important;
}

.brand-icon {
    width: 40px !important;
    height: 40px !important;
    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%) !important;
    border-radius: 8px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    color: white !important;
    font-size: 16px !important;
}

.brand-title {
    font-size: 18px !important;
    font-weight: 700 !important;
    color: #1e40af !important;
    margin: 0 !important;
    line-height: 1.2 !important;
}

.brand-subtitle {
    font-size: 12px !important;
    color: #6b7280 !important;
    margin: 0 !important;
    line-height: 1.2 !important;
}

/* الروابط */
.enhanced-link, .nav-link {
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
    padding: 10px 15px !important;
    border-radius: 8px !important;
    color: #374151 !important;
    font-weight: 500 !important;
    text-decoration: none !important;
    margin: 0 5px !important;
}

.enhanced-link:hover, .nav-link:hover {
    background: #f3f4f6 !important;
    color: #1e40af !important;
    text-decoration: none !important;
}

/* شريط التنقل السريع - مضغوط */
.quick-nav {
    background: #f8fafc !important;
    padding: 5px 0 !important;
    border-top: 1px solid #e5e7eb !important;
    min-height: 40px !important;
}

.quick-links {
    display: flex !important;
    gap: 15px !important;
    justify-content: center !important;
    flex-wrap: wrap !important;
}

.quick-link {
    display: flex !important;
    align-items: center !important;
    gap: 6px !important;
    padding: 8px 15px !important;
    background: white !important;
    color: #374151 !important;
    text-decoration: none !important;
    border-radius: 6px !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    border: 1px solid #d1d5db !important;
}

.quick-link:hover {
    background: #e5e7eb !important;
    color: #1e40af !important;
    text-decoration: none !important;
}

/* المحتوى الرئيسي */
.container, .main-container {
    background: white !important;
    border-radius: 10px !important;
    padding: 30px !important;
    margin: 20px auto !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
    max-width: 1200px !important;
}

/* العناوين - واضحة ومرئية */
h1, h2, h3, h4, h5, h6 {
    color: #1e40af !important;
    font-weight: 700 !important;
    margin-bottom: 15px !important;
    font-family: 'Cairo', 'Tajawal', sans-serif !important;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

h1 { font-size: 28px !important; }
h2 { font-size: 24px !important; }
h3 { font-size: 20px !important; }
h4 { font-size: 18px !important; }
h5 { font-size: 16px !important; }
h6 { font-size: 14px !important; }

/* النصوص - واضحة ومرئية */
p, span, div, label, a {
    color: #374151 !important;
    line-height: 1.6 !important;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* ضمان ظهور جميع النصوص */
* {
    color: inherit !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.lead {
    font-size: 18px !important;
    color: #6b7280 !important;
    margin-bottom: 20px !important;
}

/* البطاقات */
.card {
    background: white !important;
    border: 1px solid #e5e7eb !important;
    border-radius: 10px !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
    margin-bottom: 20px !important;
}

.card-header {
    background: #f8fafc !important;
    border-bottom: 1px solid #e5e7eb !important;
    padding: 15px 20px !important;
    border-radius: 10px 10px 0 0 !important;
}

.card-body {
    padding: 20px !important;
}

.card-title {
    color: #1e40af !important;
    font-weight: 600 !important;
    margin: 0 !important;
}

/* الأزرار */
.btn {
    padding: 10px 20px !important;
    border-radius: 8px !important;
    font-weight: 500 !important;
    text-decoration: none !important;
    border: none !important;
    cursor: pointer !important;
    display: inline-flex !important;
    align-items: center !important;
    gap: 8px !important;
}

.btn-primary {
    background: #3b82f6 !important;
    color: white !important;
}

.btn-primary:hover {
    background: #2563eb !important;
    color: white !important;
}

.btn-success {
    background: #10b981 !important;
    color: white !important;
}

.btn-success:hover {
    background: #059669 !important;
    color: white !important;
}

.btn-info {
    background: #06b6d4 !important;
    color: white !important;
}

.btn-info:hover {
    background: #0891b2 !important;
    color: white !important;
}

.btn-secondary {
    background: #6b7280 !important;
    color: white !important;
}

.btn-secondary:hover {
    background: #4b5563 !important;
    color: white !important;
}

.btn-outline-primary {
    background: transparent !important;
    color: #3b82f6 !important;
    border: 1px solid #3b82f6 !important;
}

.btn-outline-primary:hover {
    background: #3b82f6 !important;
    color: white !important;
}

/* النماذج */
.form-control, .form-select {
    padding: 10px 15px !important;
    border: 1px solid #d1d5db !important;
    border-radius: 8px !important;
    background: white !important;
    color: #374151 !important;
    font-size: 16px !important;
}

.form-control:focus, .form-select:focus {
    border-color: #3b82f6 !important;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
    outline: none !important;
}

.form-label {
    color: #374151 !important;
    font-weight: 500 !important;
    margin-bottom: 5px !important;
}

/* الجداول */
.table {
    background: white !important;
    border-collapse: collapse !important;
    width: 100% !important;
}

.table th {
    background: #f8fafc !important;
    color: #374151 !important;
    font-weight: 600 !important;
    padding: 12px !important;
    border-bottom: 1px solid #e5e7eb !important;
}

.table td {
    padding: 12px !important;
    border-bottom: 1px solid #f3f4f6 !important;
    color: #374151 !important;
}

/* التنبيهات */
.alert {
    padding: 15px 20px !important;
    border-radius: 8px !important;
    margin-bottom: 20px !important;
    border: none !important;
}

.alert-info {
    background: #eff6ff !important;
    color: #1e40af !important;
    border-left: 4px solid #3b82f6 !important;
}

.alert-success {
    background: #f0fdf4 !important;
    color: #166534 !important;
    border-left: 4px solid #10b981 !important;
}

.alert-warning {
    background: #fefce8 !important;
    color: #a16207 !important;
    border-left: 4px solid #eab308 !important;
}

/* الأيقونات */
i, .fas, .far, .fab {
    color: inherit !important;
}

/* قائمة المستخدم */
.user-dropdown .user-link {
    display: flex !important;
    align-items: center !important;
    gap: 10px !important;
    padding: 8px 15px !important;
    border-radius: 20px !important;
    background: #f8fafc !important;
    border: 1px solid #e5e7eb !important;
    color: #374151 !important;
    text-decoration: none !important;
}

.user-dropdown .user-link:hover {
    background: #e5e7eb !important;
    color: #1e40af !important;
}

.user-avatar {
    width: 32px !important;
    height: 32px !important;
    background: #3b82f6 !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    color: white !important;
    font-size: 14px !important;
}

/* القائمة المنسدلة */
.dropdown-menu {
    background: white !important;
    border: 1px solid #e5e7eb !important;
    border-radius: 8px !important;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
    padding: 5px 0 !important;
}

.dropdown-item {
    padding: 10px 15px !important;
    color: #374151 !important;
    text-decoration: none !important;
}

.dropdown-item:hover {
    background: #f3f4f6 !important;
    color: #1e40af !important;
}

/* التذييل */
footer {
    background: #374151 !important;
    color: white !important;
    text-align: center !important;
    padding: 20px 0 !important;
    margin-top: 40px !important;
}

/* تحسينات الاستجابة */
@media (max-width: 768px) {
    .container, .main-container {
        margin: 10px !important;
        padding: 20px !important;
    }
    
    .brand-title {
        font-size: 18px !important;
    }
    
    .brand-subtitle {
        font-size: 12px !important;
    }
    
    h1 { font-size: 24px !important; }
    h2 { font-size: 20px !important; }
    h3 { font-size: 18px !important; }
}
