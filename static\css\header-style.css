/* تصميم رأس الصفحة المحسن والإبداعي */

/* الشريط العلوي */
.top-bar {
    background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 50%, #60a5fa 100%);
    color: white;
    padding: 8px 0;
    font-size: 0.9rem;
    box-shadow: 0 2px 10px rgba(30, 58, 138, 0.3);
}

.header-info, .header-contact {
    display: flex;
    align-items: center;
    gap: 8px;
}

.separator {
    margin: 0 15px;
    opacity: 0.7;
}

/* الشريط الرئيسي المحسن */
.main-navbar {
    background: white !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    padding: 15px 0;
    border-bottom: 3px solid #f8f9fa;
}

/* الشعار المحسن */
.enhanced-brand {
    text-decoration: none !important;
    color: inherit !important;
}

.brand-container {
    display: flex;
    align-items: center;
    gap: 15px;
}

.brand-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    box-shadow: 0 4px 15px rgba(30, 64, 175, 0.3);
}

.brand-text {
    display: flex;
    flex-direction: column;
}

.brand-title {
    font-size: 1.4rem;
    font-weight: 800;
    color: #1e40af;
    line-height: 1.2;
    font-family: 'Cairo', 'Tajawal', sans-serif;
}

.brand-subtitle {
    font-size: 0.85rem;
    color: #6b7280;
    font-weight: 500;
}

/* الروابط المحسنة */
.enhanced-link {
    display: flex !important;
    align-items: center;
    gap: 8px;
    padding: 12px 20px !important;
    border-radius: 10px;
    transition: all 0.3s ease;
    color: #374151 !important;
    font-weight: 600;
    margin: 0 5px;
    text-decoration: none !important;
}

.enhanced-link:hover {
    background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%) !important;
    color: white !important;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(59, 130, 246, 0.4);
}

.enhanced-link i {
    font-size: 1.1rem;
}

/* زر التبديل المخصص */
.custom-toggler {
    border: none;
    padding: 8px;
    background: #f3f4f6;
    border-radius: 8px;
    width: 45px;
    height: 45px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 4px;
}

.custom-toggler span {
    display: block;
    width: 25px;
    height: 3px;
    background: #374151;
    border-radius: 2px;
    transition: all 0.3s ease;
}

.custom-toggler:hover span {
    background: #1e40af;
}

/* قائمة المستخدم المحسنة */
.user-dropdown .user-link {
    display: flex !important;
    align-items: center;
    gap: 10px;
    padding: 8px 15px !important;
    border-radius: 25px;
    background: #f8fafc;
    border: 2px solid #e2e8f0;
    transition: all 0.3s ease;
    color: #1e40af !important;
    text-decoration: none !important;
}

.user-dropdown .user-link:hover {
    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
    color: white !important;
    border-color: #1e40af;
    box-shadow: 0 4px 15px rgba(30, 64, 175, 0.3);
}

.user-avatar {
    width: 35px;
    height: 35px;
    background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
}

.user-name {
    font-weight: 600;
    font-size: 0.95rem;
}

/* القائمة المنسدلة المحسنة */
.enhanced-dropdown {
    background: white !important;
    border: none !important;
    border-radius: 15px !important;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15) !important;
    padding: 10px 0 !important;
    margin-top: 10px !important;
    min-width: 220px !important;
}

.enhanced-dropdown .dropdown-header {
    padding: 15px 20px;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    color: #1e40af;
    font-weight: 700;
    border-radius: 10px;
    margin: 5px 10px 10px 10px;
}

.enhanced-dropdown .dropdown-item {
    padding: 12px 20px !important;
    color: #374151 !important;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 2px 10px;
    border-radius: 8px;
}

.enhanced-dropdown .dropdown-item:hover {
    background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%) !important;
    color: white !important;
    transform: translateX(5px);
}

.enhanced-dropdown .logout-link:hover {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%) !important;
}

/* زر تسجيل الدخول */
.login-btn {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%) !important;
    color: white !important;
    padding: 10px 20px !important;
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
    text-decoration: none !important;
    display: flex;
    align-items: center;
    gap: 8px;
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(16, 185, 129, 0.4);
    color: white !important;
}

/* شريط التنقل السريع */
.quick-nav {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding: 12px 0;
    border-top: 1px solid #e5e7eb;
}

.quick-links {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

.quick-link {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    background: white;
    color: #374151;
    text-decoration: none;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
    border: 1px solid #e5e7eb;
}

.quick-link:hover {
    background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    text-decoration: none;
}

.quick-link i {
    font-size: 1rem;
}

/* تحسينات الاستجابة */
@media (max-width: 768px) {
    .top-bar {
        text-align: center;
    }
    
    .header-info, .header-contact {
        justify-content: center;
        margin: 5px 0;
    }
    
    .brand-container {
        gap: 10px;
    }
    
    .brand-icon {
        width: 40px;
        height: 40px;
        font-size: 1.2rem;
    }
    
    .brand-title {
        font-size: 1.2rem;
    }
    
    .brand-subtitle {
        font-size: 0.8rem;
    }
    
    .quick-links {
        gap: 10px;
    }
    
    .quick-link {
        font-size: 0.8rem;
        padding: 6px 12px;
    }
}

/* تأثيرات إضافية */
.modern-header {
    position: sticky;
    top: 0;
    z-index: 1000;
    background: white;
}

/* تحديث الوقت والتاريخ */
#current-time, #current-date {
    font-weight: 600;
    color: #fbbf24;
}
