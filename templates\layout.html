<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{{ title }} - نظام التدريب والتأهيل</title>
    <!-- Bootstrap RTL CSS - محلي -->
    <link rel="stylesheet" href="{{ url_for('static', filename='libs/bootstrap/bootstrap.rtl.min.css') }}">
    <!-- Font Awesome - محلي -->
    <link rel="stylesheet" href="{{ url_for('static', filename='libs/fontawesome/all.min.css') }}">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/main.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/dashboard-style.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/header-style.css') }}?v={{ range(1, 10000) | random }}">
    {% block styles %}{% endblock %}
</head>
<body>
    <header class="modern-header">
        <!-- شريط علوي بتدرج جميل -->
        <div class="top-bar">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="header-info">
                            <i class="fas fa-clock"></i>
                            <span id="current-time"></span>
                            <span class="separator">|</span>
                            <i class="fas fa-calendar"></i>
                            <span id="current-date"></span>
                        </div>
                    </div>
                    <div class="col-md-6 text-end">
                        <div class="header-contact">
                            <i class="fas fa-phone"></i>
                            <span>الدعم الفني: 123-456-789</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- الشريط الرئيسي المحسن -->
        <nav class="navbar navbar-expand-lg main-navbar">
            <div class="container">
                <!-- الشعار المحسن -->
                <a class="navbar-brand enhanced-brand" href="{{ url_for('home') }}">
                    <div class="brand-container">
                        <div class="brand-icon">
                            <i class="fas fa-graduation-cap"></i>
                        </div>
                        <div class="brand-text">
                            <div class="brand-title">نظام التدريب والتأهيل</div>
                            <div class="brand-subtitle">إدارة متطورة وذكية</div>
                        </div>
                    </div>
                </a>

                <button class="navbar-toggler custom-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>

                <div class="collapse navbar-collapse" id="navbarNav">
                    <!-- القائمة الرئيسية -->
                    <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                        <li class="nav-item">
                            <a class="nav-link enhanced-link" href="{{ url_for('home') }}">
                                <i class="fas fa-home"></i>
                                <span>الرئيسية</span>
                            </a>
                        </li>
                        {% if current_user.is_authenticated %}
                        <li class="nav-item">
                            <a class="nav-link enhanced-link" href="{{ url_for('dashboard') }}">
                                <i class="fas fa-tachometer-alt"></i>
                                <span>لوحة التحكم</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link enhanced-link" href="{{ url_for('person_data.name_analysis') }}">
                                <i class="fas fa-search-plus"></i>
                                <span>تحليل الأسماء</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link enhanced-link" href="{{ url_for('courses') }}">
                                <i class="fas fa-book-open"></i>
                                <span>الدورات</span>
                            </a>
                        </li>
                        {% endif %}
                    </ul>

                    <!-- قائمة المستخدم -->
                    <ul class="navbar-nav">
                        {% if current_user.is_authenticated %}
                        <li class="nav-item dropdown user-dropdown">
                            <a class="nav-link dropdown-toggle user-link" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                                <div class="user-avatar">
                                    <i class="fas fa-user"></i>
                                </div>
                                <span class="user-name">{{ current_user.username }}</span>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end enhanced-dropdown">
                                <li class="dropdown-header">
                                    <i class="fas fa-user-circle"></i>
                                    مرحباً {{ current_user.username }}
                                </li>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <a class="dropdown-item" href="#">
                                        <i class="fas fa-user-edit"></i>
                                        الملف الشخصي
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="#">
                                        <i class="fas fa-cog"></i>
                                        الإعدادات
                                    </a>
                                </li>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <a class="dropdown-item logout-link" href="{{ url_for('logout') }}">
                                        <i class="fas fa-sign-out-alt"></i>
                                        تسجيل الخروج
                                    </a>
                                </li>
                            </ul>
                        </li>
                        {% else %}
                        <li class="nav-item">
                            <a class="nav-link login-btn" href="{{ url_for('login') }}">
                                <i class="fas fa-sign-in-alt"></i>
                                <span>تسجيل الدخول</span>
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </div>
            </div>
        </nav>

        <!-- شريط التنقل السريع -->
        <div class="quick-nav">
            <div class="container">
                <div class="quick-links">
                    {% if current_user.is_authenticated %}
                    <a href="{{ url_for('person_data.name_analysis') }}" class="quick-link">
                        <i class="fas fa-analytics"></i>
                        <span>تحليل سريع</span>
                    </a>
                    <a href="{{ url_for('reports') }}" class="quick-link">
                        <i class="fas fa-chart-line"></i>
                        <span>التقارير</span>
                    </a>
                    <a href="{{ url_for('backup') }}" class="quick-link">
                        <i class="fas fa-shield-alt"></i>
                        <span>النسخ الاحتياطية</span>
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </header>

    <main class="container mt-4">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }} alert-dismissible fade show" style="border-radius: 15px; backdrop-filter: blur(10px); box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        {% block content %}{% endblock %}
    </main>

    <footer class="bg-dark text-white text-center py-3 mt-5">
        <div class="container">
            <p class="mb-0">جميع الحقوق محفوظة &copy; {{ now.year }} نظام التدريب والتأهيل</p>
        </div>
    </footer>

    <!-- jQuery - محلي -->
    <script src="{{ url_for('static', filename='libs/jquery/jquery-3.6.0.min.js') }}"></script>
    <!-- Bootstrap JS Bundle with Popper - محلي -->
    <script src="{{ url_for('static', filename='libs/bootstrap/bootstrap.bundle.min.js') }}"></script>
    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    <script src="{{ url_for('static', filename='js/header-functions.js') }}"></script>
    {% block scripts %}{% endblock %}
</body>
</html>
