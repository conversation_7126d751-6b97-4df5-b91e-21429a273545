/* تحسينات إضافية للتقارير التفاعلية */

/* تأثيرات الحركة المتقدمة */
@keyframes slideInFromTop {
    0% {
        transform: translateY(-100px);
        opacity: 0;
    }
    100% {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes slideInFromLeft {
    0% {
        transform: translateX(-100px);
        opacity: 0;
    }
    100% {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideInFromRight {
    0% {
        transform: translateX(100px);
        opacity: 0;
    }
    100% {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes fadeInUp {
    0% {
        transform: translateY(30px);
        opacity: 0;
    }
    100% {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

/* تطبيق التأثيرات على العناصر */
.reports-header {
    animation: slideInFromTop 0.8s ease-out;
}

.date-filter-section {
    animation: slideInFromLeft 0.8s ease-out 0.2s both;
}

.stats-grid .stat-card:nth-child(1) {
    animation: fadeInUp 0.6s ease-out 0.4s both;
}

.stats-grid .stat-card:nth-child(2) {
    animation: fadeInUp 0.6s ease-out 0.5s both;
}

.stats-grid .stat-card:nth-child(3) {
    animation: fadeInUp 0.6s ease-out 0.6s both;
}

.stats-grid .stat-card:nth-child(4) {
    animation: fadeInUp 0.6s ease-out 0.7s both;
}

.charts-section {
    animation: slideInFromRight 0.8s ease-out 0.8s both;
}

.table-card {
    animation: fadeInUp 0.6s ease-out 0.9s both;
}

.export-section {
    animation: fadeInUp 0.6s ease-out 1s both;
}

/* تحسينات إضافية للبطاقات */
.stat-card {
    position: relative;
    overflow: hidden;
}

.stat-card::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transform: rotate(45deg);
    transition: all 0.6s;
    opacity: 0;
}

.stat-card:hover::after {
    animation: shine 0.6s ease-in-out;
}

@keyframes shine {
    0% {
        transform: translateX(-100%) translateY(-100%) rotate(45deg);
        opacity: 0;
    }
    50% {
        opacity: 1;
    }
    100% {
        transform: translateX(100%) translateY(100%) rotate(45deg);
        opacity: 0;
    }
}

/* تحسينات للجداول */
.data-table {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.data-table th {
    position: relative;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.9rem;
}

.data-table th::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.5), transparent);
}

.data-table tbody tr {
    transition: all 0.3s ease;
}

.data-table tbody tr:hover {
    background: linear-gradient(135deg, #f8f6f2, #ede4d3);
    transform: scale(1.01);
    box-shadow: 0 5px 15px rgba(141, 110, 99, 0.1);
}

/* تحسينات للأزرار */
.btn-export {
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.btn-export::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
    z-index: -1;
}

.btn-export:hover::before {
    left: 100%;
}

/* تحسينات للرسوم البيانية */
.chart-card {
    position: relative;
    background: linear-gradient(135deg, #ffffff, #f8f9ff);
}

.chart-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #667eea, #764ba2, #667eea);
    background-size: 200% 100%;
    animation: gradientMove 120s ease-in-out infinite;
}

@keyframes gradientMove {
    0%, 100% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
}

/* تحسينات للتحميل */
.loading-spinner {
    position: relative;
}

.loading-spinner::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 80px;
    height: 80px;
    margin: -40px 0 0 -40px;
    border: 3px solid transparent;
    border-top: 3px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* تأثيرات النص */
.reports-title {
    background: linear-gradient(45deg, #667eea, #764ba2, #667eea);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientText 180s ease-in-out infinite;
}

@keyframes gradientText {
    0%, 100% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
}

/* تحسينات للفلاتر */
.filter-card {
    position: relative;
    overflow: hidden;
}

.filter-card::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #667eea, #764ba2, #667eea, #764ba2);
    background-size: 400% 400%;
    z-index: -1;
    animation: gradientBorder 240s ease-in-out infinite;
    border-radius: 17px;
}

@keyframes gradientBorder {
    0%, 100% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
}

/* تحسينات للأيقونات */
.stat-icon {
    position: relative;
    display: inline-block;
}

.stat-icon::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 120%;
    height: 120%;
    border: 2px solid transparent;
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: iconPulse 60s ease-in-out infinite;
}

@keyframes iconPulse {
    0%, 100% {
        border-color: transparent;
        transform: translate(-50%, -50%) scale(1);
    }
    50% {
        border-color: rgba(102, 126, 234, 0.3);
        transform: translate(-50%, -50%) scale(1.1);
    }
}

/* تحسينات للاستجابة */
@media (max-width: 768px) {
    .reports-header {
        padding: 20px;
    }
    
    .reports-title {
        font-size: 2rem;
    }
    
    .stat-card {
        margin-bottom: 20px;
    }
    
    .chart-card {
        margin-bottom: 20px;
    }
}

/* تحسينات للطباعة */
@media print {
    .reports-header::before,
    .stat-card::after,
    .chart-card::before,
    .filter-card::before {
        display: none;
    }
    
    .stat-card,
    .chart-card,
    .table-card {
        break-inside: avoid;
        animation: none;
    }
}
