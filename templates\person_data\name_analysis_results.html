{% extends "layout.html" %}

{% block head %}
<meta name="csrf-token" content="{{ csrf_token() }}">
{% endblock %}

{% block styles %}
<link rel="stylesheet" href="{{ url_for('static', filename='libs/bootstrap/bootstrap.rtl.min.css') }}">
<style>
    /* خلفية متدرجة موحدة مع صفحة البيانات الشخصية */
    body {
        background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
        min-height: 100vh;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    /* تصميم البطاقات الإحصائية موحد */
    .stats-card {
        background: #007bff;
        color: white;
        padding: 25px;
        border-radius: 15px;
        text-align: center;
        margin-bottom: 25px;
        box-shadow: 0 15px 35px rgba(0, 123, 255, 0.3);
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        position: relative;
        overflow: hidden;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .stats-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
        pointer-events: none;
    }

    .stats-card:hover {
        transform: translateY(-10px) scale(1.05);
        box-shadow: 0 25px 50px rgba(0, 123, 255, 0.4);
    }

    .stat-number {
        font-size: 2.5rem;
        font-weight: 800;
        margin: 15px 0;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        color: white;
    }

    .stat-label {
        font-size: 1rem;
        font-weight: 600;
        opacity: 0.95;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        color: white;
    }

    /* تصميم الأقسام موحد */
    .result-section {
        background: rgba(255, 255, 255, 0.95);
        border-radius: 15px;
        margin-bottom: 30px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.3);
        transition: all 0.3s ease;
    }

    .result-section:hover {
        transform: translateY(-5px);
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    }

    .section-header {
        background: #6c757d;
        color: white;
        padding: 20px 25px;
        margin-bottom: 0;
        font-weight: 700;
        font-size: 1.1rem;
        position: relative;
        overflow: hidden;
    }

    .section-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
        pointer-events: none;
    }

    /* تصميم عناصر الأسماء */
    .name-item {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 15px;
        border-right: 5px solid #28a745;
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .name-item::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, rgba(255, 255, 255, 0.5) 0%, transparent 100%);
        pointer-events: none;
    }

    .name-item:hover {
        transform: translateX(10px);
        box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15);
    }

    .corrected-name {
        border-right-color: #ffc107;
        background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    }

    .similar-name {
        border-right-color: #17a2b8;
        background: linear-gradient(135deg, #d1ecf1 0%, #74b9ff 100%);
    }

    .new-name {
        border-right-color: #dc3545;
        background: linear-gradient(135deg, #f8d7da 0%, #fd79a8 100%);
    }

    /* أزرار التصدير الحديثة */
    .btn-export {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        border: none;
        padding: 12px 25px;
        color: white;
        border-radius: 25px;
        font-weight: 600;
        box-shadow: 0 8px 16px rgba(40, 167, 69, 0.3);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .btn-export::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
        transition: left 0.5s;
    }

    .btn-export:hover {
        background: linear-gradient(135deg, #218838 0%, #1dd1a1 100%);
        color: white;
        transform: translateY(-3px);
        box-shadow: 0 12px 24px rgba(40, 167, 69, 0.4);
    }

    .btn-export:hover::before {
        left: 100%;
    }

    /* تحسين البطاقات */
    .card {
        border-radius: 15px;
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        border: none;
        transition: all 0.3s ease;
        overflow: hidden;
    }

    .card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
    }

    /* تحسين الشارات */
    .badge {
        border-radius: 20px;
        padding: 8px 15px;
        font-weight: 600;
        font-size: 0.85rem;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }

    /* تحسين التنبيهات */
    .alert {
        border-radius: 15px;
        border: none;
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
        backdrop-filter: blur(10px);
    }

    /* تحسين العنوان الرئيسي موحد */
    .page-title {
        color: white;
        font-weight: 800;
        text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        margin-bottom: 20px;
    }

    /* تحسين الحاوي الرئيسي */
    .container-fluid {
        padding: 30px;
    }

    /* تأثيرات الحركة */
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .stats-card, .result-section, .card {
        animation: fadeInUp 0.6s ease-out;
    }

    /* تحسين الجداول */
    .table {
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
    }

    .table thead th {
        background: #007bff;
        color: white;
        border: none;
        font-weight: 600;
        padding: 15px;
    }

    .table tbody td {
        padding: 15px;
        border: none;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    /* تصميم البطاقات الفرعية موحد */
    .sub-stats {
        background: rgba(255, 255, 255, 0.9);
        border-radius: 10px;
        padding: 15px;
        margin: 10px 0;
        text-align: center;
        transition: all 0.3s ease;
        border: 1px solid rgba(0, 123, 255, 0.2);
        box-shadow: 0 5px 15px rgba(0, 123, 255, 0.1);
    }

    .sub-stats:hover {
        background: rgba(255, 255, 255, 1);
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 123, 255, 0.2);
    }

    .sub-stat-number {
        font-size: 1.8rem;
        font-weight: 700;
        color: #007bff;
        margin-bottom: 5px;
    }

    .sub-stat-label {
        font-size: 0.9rem;
        color: #6c757d;
        font-weight: 500;
    }

    /* تحسين الأيقونات */
    .fas, .far {
        margin-left: 8px;
        filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header Section with Modern Design -->
    <div class="row mb-5">
        <div class="col-12">
            <div class="text-center mb-4">
                <h1 class="page-title display-4">
                    <i class="fas fa-chart-line"></i> نتائج تحليل الأسماء الذكي
                </h1>
                <div class="lead text-white-50 mb-4">
                    <i class="fas fa-file-excel"></i> الملف: <strong>{{ excel_filename }}</strong>
                </div>
                {% if selected_course %}
                <div class="alert alert-info border-0 shadow-lg" style="background: rgba(255, 255, 255, 0.9); backdrop-filter: blur(10px);">
                    <i class="fas fa-graduation-cap text-primary"></i>
                    <strong>الدورة المختارة:</strong> {{ selected_course.course_number }} - {{ selected_course.title }}
                </div>
                {% endif %}
            </div>

            <!-- Action Buttons with Modern Design -->
            <div class="d-flex justify-content-center flex-wrap gap-3 mb-4">
                <a href="{{ url_for('person_data.export_analysis_results') }}" class="btn btn-export">
                    <i class="fas fa-download"></i> تصدير النتائج الكاملة
                </a>
                <a href="{{ url_for('person_data.export_new_names_only') }}" class="btn btn-success" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); border: none; border-radius: 25px; padding: 12px 25px; font-weight: 600; box-shadow: 0 8px 16px rgba(40, 167, 69, 0.3); transition: all 0.3s ease;">
                    <i class="fas fa-file-excel"></i> تصدير الأسماء الجديدة فقط
                </a>
                {% if results.new_names or results.corrected_names %}
                <form method="post" action="{{ url_for('person_data.import_analyzed_names') }}" class="d-inline">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    {% if selected_course %}
                    <button type="submit" class="btn btn-warning" style="background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%); border: none; border-radius: 25px; padding: 12px 25px; font-weight: 600; box-shadow: 0 8px 16px rgba(255, 193, 7, 0.3); transition: all 0.3s ease; color: white;"
                            onclick="return confirm('هل تريد استيراد الأسماء الجديدة إلى قاعدة البيانات والدورة {{ selected_course.course_number }}؟')">
                        <i class="fas fa-database"></i> استيراد للقاعدة والدورة
                    </button>
                    {% else %}
                    <button type="submit" class="btn btn-warning" style="background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%); border: none; border-radius: 25px; padding: 12px 25px; font-weight: 600; box-shadow: 0 8px 16px rgba(255, 193, 7, 0.3); transition: all 0.3s ease; color: white;"
                            onclick="return confirm('هل تريد استيراد الأسماء الجديدة إلى قاعدة البيانات؟')">
                        <i class="fas fa-database"></i> استيراد الأسماء الجديدة
                    </button>
                    {% endif %}
                </form>
                {% endif %}
                <a href="{{ url_for('person_data.name_analysis') }}" class="btn btn-secondary" style="background: linear-gradient(135deg, #6c757d 0%, #495057 100%); border: none; border-radius: 25px; padding: 12px 25px; font-weight: 600; box-shadow: 0 8px 16px rgba(108, 117, 125, 0.3); transition: all 0.3s ease;">
                    <i class="fas fa-arrow-right"></i> تحليل جديد
                </a>
            </div>
        </div>
    </div>

    <!-- Statistics Cards with Modern Design -->
    <div class="row mb-5">
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="stats-card text-center">
                <div class="mb-3">
                    <i class="fas fa-list-alt fa-2x" style="opacity: 0.8;"></i>
                </div>
                <div class="stat-number">{{ results.statistics.get('total_processed', 0) }}</div>
                <div class="stat-label">إجمالي السجلات المعالجة</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="stats-card text-center">
                <div class="mb-3">
                    <i class="fas fa-spell-check fa-2x" style="opacity: 0.8;"></i>
                </div>
                <div class="stat-number">{{ results.statistics.get('corrected_count', 0) }}</div>
                <div class="stat-label">الأسماء المصححة</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="stats-card text-center">
                <div class="mb-3">
                    <i class="fas fa-check-circle fa-2x" style="opacity: 0.8;"></i>
                </div>
                <div class="stat-number">{{ results.smart_statistics.in_db_count if results.smart_statistics else results.statistics.get('exact_matches_count', 0) }}</div>
                <div class="stat-label">موجود في قاعدة البيانات</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="stats-card text-center">
                <div class="mb-3">
                    <i class="fas fa-plus-circle fa-2x" style="opacity: 0.8;"></i>
                </div>
                <div class="stat-number">{{ results.smart_statistics.not_in_db_count if results.smart_statistics else results.statistics.get('new_records_count', 0) }}</div>
                <div class="stat-label">غير موجود في قاعدة البيانات</div>
            </div>
        </div>
        {% if results.smart_statistics %}
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="stats-card text-center">
                <div class="mb-3">
                    <i class="fas fa-graduation-cap fa-2x" style="opacity: 0.8;"></i>
                </div>
                <div class="stat-number">{{ results.smart_statistics.in_course_count }}</div>
                <div class="stat-label">موجود في الدورة</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="stats-card text-center">
                <div class="mb-3">
                    <i class="fas fa-user-plus fa-2x" style="opacity: 0.8;"></i>
                </div>
                <div class="stat-number">{{ results.smart_statistics.not_in_course_count }}</div>
                <div class="stat-label">سيتم إضافته للدورة</div>
            </div>
        </div>
        {% endif %}
    </div>

    <!-- Advanced Duplicate Detection Cards -->
    {% if results.statistics.get('has_national_id_column', False) or results.statistics.get('has_phone_column', False) or results.statistics.get('has_military_id_column', False) %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-warning">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-search"></i> نتائج فحص التطابق المتقدم
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-2">
                            <div class="card text-center border-danger">
                                <div class="card-body">
                                    <h5 class="text-danger">{{ results.statistics.get('blocked_duplicates_count', 0) }}</h5>
                                    <small class="text-muted">سجلات مرفوضة</small>
                                    <br><small class="text-danger">تطابق في البيانات</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="card text-center border-success">
                                <div class="card-body">
                                    <h5 class="text-success">{{ results.statistics.get('allowed_duplicates_count', 0) }}</h5>
                                    <small class="text-muted">أسماء مكررة مسموحة</small>
                                    <br><small class="text-success">بيانات مختلفة</small>
                                </div>
                            </div>
                        </div>
                        {% if results.statistics.get('has_national_id_column', False) %}
                        <div class="col-md-2">
                            <div class="card text-center border-info">
                                <div class="card-body">
                                    <h5 class="text-info">{{ results.statistics.get('name_national_id_matches', 0) + results.statistics.get('national_id_only_matches', 0) }}</h5>
                                    <small class="text-muted">تطابق رقم وطني</small>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                        {% if results.statistics.get('has_phone_column', False) %}
                        <div class="col-md-2">
                            <div class="card text-center border-info">
                                <div class="card-body">
                                    <h5 class="text-info">{{ results.statistics.get('name_phone_matches', 0) + results.statistics.get('phone_only_matches', 0) }}</h5>
                                    <small class="text-muted">تطابق رقم هاتف</small>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                        {% if results.statistics.get('has_military_id_column', False) %}
                        <div class="col-md-2">
                            <div class="card text-center border-info">
                                <div class="card-body">
                                    <h5 class="text-info">{{ results.statistics.get('name_military_id_matches', 0) + results.statistics.get('military_id_only_matches', 0) }}</h5>
                                    <small class="text-muted">تطابق رقم عسكري</small>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    {% if results.course_analysis and results.course_analysis.selected_course %}
    <!-- تحليل الدورة المختارة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-primary">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-graduation-cap"></i> تحليل الدورة المختارة
                    </h5>
                </div>
                <div class="card-body">
                    <!-- معلومات الدورة -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <h6 class="text-primary">معلومات الدورة:</h6>
                            <p class="mb-1"><strong>رقم الدورة:</strong> {{ results.course_analysis.selected_course.course_number }}</p>
                            <p class="mb-1"><strong>اسم الدورة:</strong> {{ results.course_analysis.selected_course.title }}</p>
                            <p class="mb-1"><strong>الجهة:</strong> {{ results.course_analysis.selected_course.agency or 'غير محدد' }}</p>
                            <p class="mb-0"><strong>المركز:</strong> {{ results.course_analysis.selected_course.center_name or 'غير محدد' }}</p>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-success">إحصائيات المشاركين:</h6>
                            {% if results.course_analysis.participants_summary %}
                            <p class="mb-1"><strong>المشاركين الحاليين:</strong>
                                <span class="badge bg-info">{{ results.course_analysis.participants_summary.current_participants_count }}</span>
                            </p>
                            <p class="mb-1"><strong>المشاركين الجدد:</strong>
                                <span class="badge bg-success">{{ results.course_analysis.participants_summary.new_participants_count }}</span>
                            </p>
                            <p class="mb-1"><strong>المشاركين المكررين:</strong>
                                <span class="badge bg-warning">{{ results.course_analysis.participants_summary.duplicate_participants_count }}</span>
                            </p>
                            <p class="mb-0"><strong>إجمالي بعد الاستيراد:</strong>
                                <span class="badge bg-primary">{{ results.course_analysis.participants_summary.total_after_import }}</span>
                            </p>
                            {% endif %}
                        </div>
                    </div>

                    <!-- تحذيرات المشاركين المكررين -->
                    {% if results.course_analysis.duplicate_participants %}
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>تحذير:</strong> تم العثور على {{ results.course_analysis.duplicate_participants|length }} مشارك مكرر في كشف الدورة.
                        هؤلاء المشاركين موجودين مسبقاً في الدورة ولن يتم إضافتهم مرة أخرى.
                    </div>

                    <!-- عرض المشاركين المكررين -->
                    <div class="mb-3">
                        <h6 class="text-warning">المشاركين المكررين في الدورة:</h6>
                        <div class="row">
                            {% for duplicate in results.course_analysis.duplicate_participants[:5] %}
                            <div class="col-md-6 mb-2">
                                <div class="card border-warning">
                                    <div class="card-body p-2">
                                        <h6 class="card-title text-warning mb-1">{{ duplicate.name }}</h6>
                                        <small class="text-muted">{{ duplicate.reason }}</small>
                                        <br><small class="badge bg-warning">موجود في الصف {{ duplicate.excel_record.row_index }}</small>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        {% if results.course_analysis.duplicate_participants|length > 5 %}
                        <p class="text-muted text-center mt-2">
                            <i class="fas fa-info-circle"></i>
                            يتم عرض أول 5 مشاركين مكررين فقط. للاطلاع على القائمة الكاملة، قم بتصدير النتائج.
                        </p>
                        {% endif %}
                    </div>
                    {% endif %}

                    <!-- المشاركين الجدد للدورة -->
                    {% if results.course_analysis.new_participants %}
                    <div class="mb-3">
                        <h6 class="text-success">المشاركين الجدد الذين سيتم إضافتهم للدورة:</h6>
                        <div class="row">
                            {% for new_participant in results.course_analysis.new_participants[:5] %}
                            <div class="col-md-6 mb-2">
                                <div class="card border-success">
                                    <div class="card-body p-2">
                                        <h6 class="card-title text-success mb-1">{{ new_participant.name }}</h6>
                                        <small class="badge bg-success">{{ new_participant.type }}</small>
                                        <br><small class="text-muted">من الصف {{ new_participant.excel_record.row_index }}</small>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        {% if results.course_analysis.new_participants|length > 5 %}
                        <p class="text-muted text-center mt-2">
                            <i class="fas fa-info-circle"></i>
                            يتم عرض أول 5 مشاركين جدد فقط. للاطلاع على القائمة الكاملة، قم بتصدير النتائج.
                        </p>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Similarity Statistics -->
    <div class="row mb-4">
        <div class="col-md-2">
            <div class="sub-stats">
                <div class="sub-stat-number">{{ results.statistics.get('triple_similarity_count', 0) }}</div>
                <div class="sub-stat-label">تشابه ثلاثي</div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="sub-stats">
                <div class="sub-stat-number">{{ results.statistics.get('quadruple_similarity_count', 0) }}</div>
                <div class="sub-stat-label">تشابه رباعي</div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="sub-stats">
                <div class="sub-stat-number">{{ results.statistics.get('quintuple_similarity_count', 0) }}</div>
                <div class="sub-stat-label">تشابه خماسي</div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="sub-stats">
                <div class="sub-stat-number">{{ results.statistics.get('full_with_title_count', 0) }}</div>
                <div class="sub-stat-label">تشابه كامل</div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="sub-stats">
                <div class="sub-stat-number">{{ results.statistics.get('six_plus_count', 0) }}</div>
                <div class="sub-stat-label">أسماء طويلة</div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="sub-stats">
                <div class="sub-stat-number">{{ results.total_db_names }}</div>
                <div class="sub-stat-label">في قاعدة البيانات</div>
            </div>
        </div>
    </div>

    <!-- Corrected Names Section -->
    {% if results.corrected_names %}
    <div class="result-section">
        <div class="section-header">
            <h4 class="mb-0">
                <i class="fas fa-spell-check"></i> الأسماء المصححة
                <span class="badge bg-warning">{{ results.corrected_names|length }}</span>
            </h4>
        </div>
        <div class="p-3">
            <div class="row">
                {% for correction in results.corrected_names[:10] %}
                <div class="col-md-6 mb-2">
                    <div class="name-item corrected-name">
                        <strong>الأصلي:</strong> {{ correction.original }}<br>
                        <strong>المصحح:</strong> <span class="text-success">{{ correction.corrected }}</span>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% if results.corrected_names|length > 10 %}
            <p class="text-muted text-center mt-3">
                <i class="fas fa-info-circle"></i>
                يتم عرض أول 10 أسماء فقط. للاطلاع على جميع النتائج، قم بتصدير الملف.
            </p>
            {% endif %}
        </div>
    </div>
    {% endif %}

    <!-- Smart Categories - الفئات الذكية -->

    <!-- موجود في قاعدة البيانات ومكرر في الدورة -->
    {% if results.duplicate_in_course %}
    <div class="result-section">
        <div class="section-header">
            <h4 class="mb-0">
                <i class="fas fa-exclamation-triangle"></i> موجود في قاعدة البيانات + مكرر في الدورة
                <span class="badge bg-light text-dark">{{ results.duplicate_in_course|length }}</span>
            </h4>
        </div>
        <div class="p-3">
            <div class="alert alert-danger">
                <i class="fas fa-ban"></i>
                <strong>تحذير:</strong> هؤلاء الأشخاص موجودين مسبق<|im_start|> في الدورة ولن يتم إضافتهم مرة أخرى
            </div>
            {% for record in results.duplicate_in_course[:10] %}
            <div class="card mb-2 border-danger">
                <div class="card-body p-2">
                    <div class="row">
                        <div class="col-md-8">
                            <strong class="text-danger">{{ record.corrected_name }}</strong>
                            <small class="text-muted d-block">الصف: {{ record.row_index }}</small>
                            <small class="text-danger">{{ record.action_needed }}</small>
                        </div>
                        <div class="col-md-4 text-end">
                            <span class="badge bg-danger">مكرر في الدورة</span>
                            {% if record.course_match and record.course_match.created_at %}
                            <br><small class="text-muted">تاريخ الدخول: {{ record.course_match.created_at.strftime('%Y-%m-%d') }}</small>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
            {% if results.duplicate_in_course|length > 10 %}
            <p class="text-muted text-center mt-3">
                <i class="fas fa-info-circle"></i>
                يتم عرض أول 10 سجلات فقط. للاطلاع على جميع النتائج، قم بتصدير الملف.
            </p>
            {% endif %}
        </div>
    </div>
    {% endif %}

    <!-- موجود في قاعدة البيانات وسيتم إضافته للدورة -->
    {% if results.in_db_not_in_course %}
    <div class="result-section">
        <div class="section-header">
            <h4 class="mb-0">
                <i class="fas fa-user-plus"></i> موجود في قاعدة البيانات - سيتم إضافته للدورة
                <span class="badge bg-light text-dark">{{ results.in_db_not_in_course|length }}</span>
            </h4>
        </div>
        <div class="p-3">
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i>
                <strong>إجراء:</strong> هؤلاء الأشخاص موجودين في قاعدة البيانات وسيتم إضافتهم للدورة
            </div>
            {% for record in results.in_db_not_in_course[:10] %}
            <div class="card mb-2 border-success">
                <div class="card-body p-2">
                    <div class="row">
                        <div class="col-md-8">
                            <strong class="text-success">{{ record.corrected_name }}</strong>
                            <small class="text-muted d-block">الصف: {{ record.row_index }}</small>
                            <small class="text-success">{{ record.action_needed }}</small>
                        </div>
                        <div class="col-md-4 text-end">
                            <span class="badge bg-success">سيتم إضافته للدورة</span>
                            {% if record.db_match %}
                            <br><small class="text-muted">ID: {{ record.db_match.id }}</small>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
            {% if results.in_db_not_in_course|length > 10 %}
            <p class="text-muted text-center mt-3">
                <i class="fas fa-info-circle"></i>
                يتم عرض أول 10 سجلات فقط. للاطلاع على جميع النتائج، قم بتصدير الملف.
            </p>
            {% endif %}
        </div>
    </div>
    {% endif %}

    <!-- Exact Matches Section -->
    {% if results.exact_matches %}
    <div class="result-section">
        <div class="section-header">
            <h4 class="mb-0">
                <i class="fas fa-check-circle"></i> موجود في قاعدة البيانات
                <span class="badge bg-success">{{ results.exact_matches|length }}</span>
            </h4>
        </div>
        <div class="p-3">
            <div class="row">
                {% for match in results.exact_matches[:10] %}
                <div class="col-md-12 mb-3">
                    <div class="card border-success">
                        <div class="card-body p-3">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="text-success mb-2">
                                        <i class="fas fa-check-circle"></i> من ملف Excel:
                                    </h6>
                                    <p class="mb-1"><strong>{{ match.excel_name }}</strong></p>
                                    <small class="text-muted">الصف: {{ match.excel_record.row_index }}</small>
                                    {% if match.excel_record.national_id %}
                                    <br><small class="badge bg-info">رقم وطني: {{ match.excel_record.national_id }}</small>
                                    {% endif %}
                                    {% if match.excel_record.phone %}
                                    <br><small class="badge bg-success">هاتف: {{ match.excel_record.phone }}</small>
                                    {% endif %}
                                    {% if match.excel_record.military_id %}
                                    <br><small class="badge bg-warning">عسكري: {{ match.excel_record.military_id }}</small>
                                    {% endif %}
                                </div>
                                <div class="col-md-6">
                                    <h6 class="text-primary mb-2">
                                        <i class="fas fa-database"></i> من قاعدة البيانات:
                                    </h6>
                                    <p class="mb-1"><strong>{{ match.db_name }}</strong></p>
                                    <small class="text-muted">ID: {{ match.db_record.id }}</small>
                                    {% if match.db_record.national_id %}
                                    <br><small class="badge bg-info">رقم وطني: {{ match.db_record.national_id }}</small>
                                    {% endif %}
                                    {% if match.db_record.phone %}
                                    <br><small class="badge bg-success">هاتف: {{ match.db_record.phone }}</small>
                                    {% endif %}
                                    {% if match.db_record.military_id %}
                                    <br><small class="badge bg-warning">عسكري: {{ match.db_record.military_id }}</small>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% if results.exact_matches|length > 10 %}
            <p class="text-muted text-center mt-3">
                <i class="fas fa-info-circle"></i>
                يتم عرض أول 10 مطابقات فقط. للاطلاع على جميع النتائج، قم بتصدير الملف.
            </p>
            {% endif %}
        </div>
    </div>
    {% endif %}

    <!-- Blocked Duplicates Section -->
    {% if results.blocked_duplicates %}
    <div class="result-section">
        <div class="section-header">
            <h4 class="mb-0">
                <i class="fas fa-ban"></i> السجلات المرفوضة (تطابق في البيانات الشخصية)
                <span class="badge bg-light text-dark">{{ results.blocked_duplicates|length }}</span>
            </h4>
        </div>
        <div class="p-3">
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i>
                <strong>تحذير:</strong> هذه السجلات لن يتم استيرادها بسبب التطابق في البيانات الشخصية
            </div>
            {% for blocked in results.blocked_duplicates[:10] %}
            <div class="card mb-3 border-danger">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-danger">السجل من Excel:</h6>
                            <p><strong>الاسم:</strong> {{ blocked.corrected_name }}</p>
                            {% if blocked.excel_record.national_id %}
                            <p><strong>الرقم الوطني:</strong> {{ blocked.excel_record.national_id }}</p>
                            {% endif %}
                            {% if blocked.excel_record.phone %}
                            <p><strong>رقم الهاتف:</strong> {{ blocked.excel_record.phone }}</p>
                            {% endif %}
                            {% if blocked.excel_record.military_id %}
                            <p><strong>الرقم العسكري:</strong> {{ blocked.excel_record.military_id }}</p>
                            {% endif %}
                            <small class="text-muted">الصف: {{ blocked.excel_record.row_index }}</small>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-warning">سبب الرفض:</h6>
                            <p class="text-danger">{{ blocked.reason }}</p>
                            <div class="mt-2">
                                {% for dup_type in blocked.duplicate_types %}
                                <span class="badge bg-danger me-1">{{ dup_type }}</span>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
            {% if results.blocked_duplicates|length > 10 %}
            <p class="text-muted text-center mt-3">
                <i class="fas fa-info-circle"></i>
                يتم عرض أول 10 سجلات فقط. للاطلاع على جميع النتائج، قم بتصدير الملف.
            </p>
            {% endif %}
        </div>
    </div>
    {% endif %}

    <!-- Allowed Duplicates Section -->
    {% if results.allowed_duplicates %}
    <div class="result-section">
        <div class="section-header">
            <h4 class="mb-0">
                <i class="fas fa-check-circle"></i> الأسماء المكررة المسموحة (بيانات مختلفة)
                <span class="badge bg-light text-dark">{{ results.allowed_duplicates|length }}</span>
            </h4>
        </div>
        <div class="p-3">
            <div class="alert alert-success">
                <i class="fas fa-info-circle"></i>
                <strong>ملاحظة:</strong> هذه الأسماء مكررة لكن البيانات الشخصية مختلفة، لذا يمكن إضافتها
            </div>
            {% for allowed in results.allowed_duplicates[:10] %}
            <div class="card mb-3 border-success">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <h6 class="text-success">السجل المسموح:</h6>
                            <p><strong>الاسم:</strong> {{ allowed.corrected_name }}</p>
                            {% if allowed.excel_record.national_id %}
                            <p><strong>الرقم الوطني:</strong> {{ allowed.excel_record.national_id }}</p>
                            {% endif %}
                            {% if allowed.excel_record.phone %}
                            <p><strong>رقم الهاتف:</strong> {{ allowed.excel_record.phone }}</p>
                            {% endif %}
                            {% if allowed.excel_record.military_id %}
                            <p><strong>الرقم العسكري:</strong> {{ allowed.excel_record.military_id }}</p>
                            {% endif %}
                            <small class="text-muted">الصف: {{ allowed.excel_record.row_index }}</small>
                        </div>
                        <div class="col-md-4">
                            <h6 class="text-success">السبب:</h6>
                            <p class="text-success">{{ allowed.reason }}</p>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
            {% if results.allowed_duplicates|length > 10 %}
            <p class="text-muted text-center mt-3">
                <i class="fas fa-info-circle"></i>
                يتم عرض أول 10 سجلات فقط. للاطلاع على جميع النتائج، قم بتصدير الملف.
            </p>
            {% endif %}
        </div>
    </div>
    {% endif %}

    <!-- New Records Section -->
    {% if results.new_records %}
    <div class="result-section">
        <div class="section-header">
            <h4 class="mb-0">
                <i class="fas fa-plus-circle"></i> غير موجود في قاعدة البيانات
                <span class="badge bg-primary">{{ results.new_records|length }}</span>
            </h4>
        </div>
        <div class="p-3">
            {% for record in results.new_records[:20] %}
            <div class="card mb-2 border-primary">
                <div class="card-body p-2">
                    <div class="row">
                        <div class="col-md-8">
                            <strong>{{ record.corrected_name }}</strong>
                            <small class="text-muted d-block">الصف: {{ record.excel_record.row_index }}</small>
                        </div>
                        <div class="col-md-4 text-end">
                            {% if record.excel_record.national_id %}
                            <small class="badge bg-info">رقم وطني: {{ record.excel_record.national_id }}</small>
                            {% endif %}
                            {% if record.excel_record.phone %}
                            <small class="badge bg-success">هاتف: {{ record.excel_record.phone }}</small>
                            {% endif %}
                            {% if record.excel_record.military_id %}
                            <small class="badge bg-warning">عسكري: {{ record.excel_record.military_id }}</small>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
            {% if results.new_records|length > 20 %}
            <p class="text-muted text-center mt-3">
                <i class="fas fa-info-circle"></i>
                يتم عرض أول 20 سجل فقط. للاطلاع على جميع النتائج، قم بتصدير الملف.
            </p>
            {% endif %}
        </div>
    </div>
    {% endif %}

    <!-- Advanced Duplicate Matches Sections -->
    {% for match_type, matches in results.duplicate_matches.items() %}
    {% if matches %}
    <div class="result-section">
        <div class="section-header">
            <h4 class="mb-0">
                <i class="fas fa-exclamation-triangle"></i>
                {% if match_type == 'name_national_id' %}
                تطابق في الاسم والرقم الوطني
                {% elif match_type == 'name_phone' %}
                تطابق في الاسم ورقم الهاتف
                {% elif match_type == 'name_military_id' %}
                تطابق في الاسم والرقم العسكري
                {% elif match_type == 'national_id_only' %}
                تطابق في الرقم الوطني فقط (أسماء مختلفة)
                {% elif match_type == 'phone_only' %}
                تطابق في رقم الهاتف فقط (أسماء مختلفة)
                {% elif match_type == 'military_id_only' %}
                تطابق في الرقم العسكري فقط (أسماء مختلفة)
                {% endif %}
                <span class="badge bg-light text-dark">{{ matches|length }}</span>
            </h4>
        </div>
        <div class="p-3">
            <div class="alert alert-warning">
                <i class="fas fa-info-circle"></i>
                <strong>تنبيه:</strong> {{ matches[0].match_details if matches }}
            </div>
            {% for match in matches[:10] %}
            <div class="card mb-3 border-warning">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary">السجل من Excel:</h6>
                            <p><strong>الاسم:</strong> {{ match.excel_record.corrected_name }}</p>
                            {% if match.excel_record.national_id %}
                            <p><strong>الرقم الوطني:</strong> {{ match.excel_record.national_id }}</p>
                            {% endif %}
                            {% if match.excel_record.phone %}
                            <p><strong>رقم الهاتف:</strong> {{ match.excel_record.phone }}</p>
                            {% endif %}
                            {% if match.excel_record.military_id %}
                            <p><strong>الرقم العسكري:</strong> {{ match.excel_record.military_id }}</p>
                            {% endif %}
                            <small class="text-muted">الصف: {{ match.excel_record.row_index }}</small>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-success">السجل من قاعدة البيانات:</h6>
                            <p><strong>الاسم:</strong> {{ match.db_record.name }}</p>
                            {% if match.db_record.national_id %}
                            <p><strong>الرقم الوطني:</strong> {{ match.db_record.national_id }}</p>
                            {% endif %}
                            {% if match.db_record.phone %}
                            <p><strong>رقم الهاتف:</strong> {{ match.db_record.phone }}</p>
                            {% endif %}
                            {% if match.db_record.military_id %}
                            <p><strong>الرقم العسكري:</strong> {{ match.db_record.military_id }}</p>
                            {% endif %}
                            <small class="text-muted">ID: {{ match.db_record.id }}</small>
                        </div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-12">
                            <span class="badge bg-warning text-dark">{{ match.match_details }}</span>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
            {% if matches|length > 10 %}
            <p class="text-muted text-center mt-3">
                <i class="fas fa-info-circle"></i>
                يتم عرض أول 10 تطابقات فقط. للاطلاع على جميع النتائج، قم بتصدير الملف.
            </p>
            {% endif %}
        </div>
    </div>
    {% endif %}
    {% endfor %}

    <!-- Similarity Sections -->
    {% for similarity_type, similarity_data in results.similarity_matches.items() %}
    {% if similarity_data and similarity_type != 'six_plus' %}
    <div class="result-section">
        <div class="section-header">
            <h4 class="mb-0">
                <i class="fas fa-layer-group"></i>
                {% if similarity_type == 'triple' %}التشابه الثلاثي{% endif %}
                {% if similarity_type == 'quadruple' %}التشابه الرباعي{% endif %}
                {% if similarity_type == 'quintuple' %}التشابه الخماسي{% endif %}
                {% if similarity_type == 'full_with_title' %}التشابه الكامل مع اللقب{% endif %}
                <span class="badge bg-info">{{ similarity_data|length }}</span>
            </h4>
        </div>
        <div class="p-3">
            {% for match in similarity_data[:5] %}
            <div class="name-item similar-name mb-3">
                <div class="row">
                    <div class="col-md-5">
                        <strong>من Excel:</strong><br>
                        {{ match.excel_name }}
                    </div>
                    <div class="col-md-5">
                        <strong>من قاعدة البيانات:</strong><br>
                        {{ match.db_name }}
                    </div>
                    <div class="col-md-2">
                        <span class="badge badge-custom bg-info">
                            {{ match.common_parts|length }} أجزاء مشتركة
                        </span>
                    </div>
                </div>
            </div>
            {% endfor %}
            {% if similarity_data|length > 5 %}
            <p class="text-muted text-center mt-3">
                <i class="fas fa-info-circle"></i>
                يتم عرض أول 5 تشابهات فقط. للاطلاع على جميع النتائج، قم بتصدير الملف.
            </p>
            {% endif %}
        </div>
    </div>
    {% endif %}
    {% endfor %}

    <!-- Long Names Section -->
    {% if results.similarity_matches.six_plus %}
    <div class="result-section">
        <div class="section-header">
            <h4 class="mb-0">
                <i class="fas fa-text-width"></i> الأسماء الطويلة (أكثر من 6 أجزاء)
                <span class="badge bg-warning">{{ results.similarity_matches.six_plus|length }}</span>
            </h4>
        </div>
        <div class="p-3">
            {% for item in results.similarity_matches.six_plus[:10] %}
            <div class="name-item mb-2">
                <strong>{{ item.name }}</strong>
                <span class="badge bg-secondary">{{ item.parts_count }} أجزاء</span>
                <br>
                <small class="text-muted">{{ item.parts|join(', ') }}</small>
            </div>
            {% endfor %}
            {% if results.similarity_matches.six_plus|length > 10 %}
            <p class="text-muted text-center mt-3">
                <i class="fas fa-info-circle"></i>
                يتم عرض أول 10 أسماء فقط. للاطلاع على جميع النتائج، قم بتصدير الملف.
            </p>
            {% endif %}
        </div>
    </div>
    {% endif %}

    <!-- Export Section -->
    <div class="mt-5 mb-4">
        <div class="card">
            <div class="card-body">
                <h5 class="text-primary text-center mb-4">
                    <i class="fas fa-file-excel"></i> خيارات التصدير والاستيراد
                </h5>

                <div class="row">
                    <div class="col-md-4 mb-3">
                        <div class="card h-100 border-primary">
                            <div class="card-body text-center">
                                <i class="fas fa-download text-primary mb-3" style="font-size: 2rem;"></i>
                                <h6>التقرير الكامل</h6>
                                <p class="text-muted small">جميع النتائج والإحصائيات في ملف Excel منظم</p>
                                <a href="{{ url_for('person_data.export_analysis_results') }}" class="btn btn-primary">
                                    <i class="fas fa-download"></i> تحميل
                                </a>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4 mb-3">
                        <div class="card h-100 border-success">
                            <div class="card-body text-center">
                                <i class="fas fa-file-excel text-success mb-3" style="font-size: 2rem;"></i>
                                <h6>الأسماء الجديدة فقط</h6>
                                <p class="text-muted small">الأسماء غير الموجودة في قاعدة البيانات للمراجعة</p>
                                <a href="{{ url_for('person_data.export_new_names_only') }}" class="btn btn-success">
                                    <i class="fas fa-file-excel"></i> تحميل
                                </a>
                            </div>
                        </div>
                    </div>

                    {% if results.new_records or results.allowed_duplicates or results.corrected_names %}
                    <div class="col-md-4 mb-3">
                        <div class="card h-100 border-warning">
                            <div class="card-body text-center">
                                <i class="fas fa-database text-warning mb-3" style="font-size: 2rem;"></i>
                                <h6>استيراد مباشر</h6>
                                <p class="text-muted small">إضافة السجلات الجديدة والمسموحة مباشرة إلى قاعدة البيانات</p>
                                <form method="post" action="{{ url_for('person_data.import_analyzed_names') }}">
                                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                    <button type="submit" class="btn btn-warning"
                                            onclick="return confirm('هل تريد استيراد {{ (results.new_records|length if results.new_records else 0) + (results.allowed_duplicates|length if results.allowed_duplicates else 0) + (results.corrected_names|length if results.corrected_names else 0) }} سجل جديد إلى قاعدة البيانات{% if selected_course %} وإلى الدورة {{ selected_course.title }}{% endif %}؟')">
                                        <i class="fas fa-database"></i> استيراد
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>

                <!-- إحصائيات التحليل -->
                <div class="mt-4 p-3 bg-light rounded">
                    <h6 class="text-primary mb-3 text-center">
                        <i class="fas fa-chart-pie"></i> ملخص التحليل بالنسب المئوية
                    </h6>
                    <div class="row text-center">
                        <div class="col-md-3">
                            <div class="border-end">
                                <h5 class="text-success">{{ "%.1f"|format((results.statistics.new_records_count / results.statistics.total_processed * 100) if results.statistics.total_processed > 0 else 0) }}%</h5>
                                <small class="text-muted">سجلات جديدة</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="border-end">
                                <h5 class="text-info">{{ "%.1f"|format((results.statistics.exact_matches_count / results.statistics.total_processed * 100) if results.statistics.total_processed > 0 else 0) }}%</h5>
                                <small class="text-muted">مطابقات تامة</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="border-end">
                                <h5 class="text-warning">{{ "%.1f"|format((results.statistics.get('corrected_count', 0) / results.statistics.get('total_processed', 1) * 100) if results.statistics.get('total_processed', 0) > 0 else 0) }}%</h5>
                                <small class="text-muted">أسماء مصححة</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <h5 class="text-primary">{{ "%.1f"|format(((results.statistics.get('triple_similarity_count', 0) + results.statistics.get('quadruple_similarity_count', 0) + results.statistics.get('quintuple_similarity_count', 0)) / results.statistics.get('total_processed', 1) * 100) if results.statistics.get('total_processed', 0) > 0 else 0) }}%</h5>
                            <small class="text-muted">تشابهات</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
