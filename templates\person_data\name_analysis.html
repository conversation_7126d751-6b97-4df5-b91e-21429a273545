{% extends "layout.html" %}

{% block head %}
<meta name="csrf-token" content="{{ csrf_token() }}">
{% endblock %}

{% block styles %}
<link rel="stylesheet" href="{{ url_for('static', filename='libs/bootstrap/bootstrap.rtl.min.css') }}">
<!-- الخط العربي المحسن -->
<link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;800;900&family=Cairo:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
<style>
    /* تطبيق الخط العربي المحسن */
    body, h1, h2, h3, h4, h5, h6, p, span, div, label, button, input, select, textarea {
        font-family: 'Cairo', 'Tajawal', sans-serif !important;
    }

    /* خلفية ثابتة مع أشعة الشمس الخافتة */
    body {
        background:
            linear-gradient(135deg, #1e3a8a 0%, #3b82f6 25%, #60a5fa 50%, #93c5fd 75%, #dbeafe 100%);
        min-height: 100vh;
        position: relative;
        overflow-x: hidden;
        line-height: 1.6;
    }

    /* أشعة الشمس الخافتة والمبدعة */
    body::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background:
            radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.08) 0%, transparent 25%),
            radial-gradient(circle at 80% 30%, rgba(255, 255, 255, 0.06) 0%, transparent 30%),
            radial-gradient(circle at 40% 70%, rgba(255, 255, 255, 0.05) 0%, transparent 35%),
            radial-gradient(circle at 70% 80%, rgba(255, 255, 255, 0.07) 0%, transparent 25%),
            radial-gradient(circle at 10% 60%, rgba(255, 255, 255, 0.04) 0%, transparent 40%),
            radial-gradient(circle at 90% 10%, rgba(255, 255, 255, 0.06) 0%, transparent 30%),
            /* أشعة خطية خافتة */
            linear-gradient(45deg, transparent 40%, rgba(255, 255, 255, 0.02) 50%, transparent 60%),
            linear-gradient(-45deg, transparent 40%, rgba(255, 255, 255, 0.02) 50%, transparent 60%),
            linear-gradient(135deg, transparent 40%, rgba(255, 255, 255, 0.015) 50%, transparent 60%),
            linear-gradient(-135deg, transparent 40%, rgba(255, 255, 255, 0.015) 50%, transparent 60%);
        pointer-events: none;
        z-index: -1;
    }



    body::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background:
            radial-gradient(circle at 20% 80%, rgba(30, 58, 138, 0.3) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(59, 130, 246, 0.2) 0%, transparent 50%),
            radial-gradient(circle at 40% 40%, rgba(147, 197, 253, 0.2) 0%, transparent 50%),
            radial-gradient(circle at 60% 70%, rgba(219, 234, 254, 0.1) 0%, transparent 50%);
        pointer-events: none;
        z-index: -1;

    }



    /* تصميم حديث للبطاقات بخلفية بيضاء */
    .analysis-card {
        border-radius: 30px;
        box-shadow:
            0 25px 50px rgba(30, 64, 175, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.8);
        transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        border: 2px solid rgba(59, 130, 246, 0.2);
        overflow: hidden;
        backdrop-filter: blur(10px);
        background: rgba(255, 255, 255, 0.95);

        position: relative;
    }



    .analysis-card:hover {
        box-shadow:
            0 35px 70px rgba(30, 64, 175, 0.3),
            inset 0 2px 0 rgba(255, 255, 255, 0.9);
        border-color: rgba(59, 130, 246, 0.4);
        background: rgba(255, 255, 255, 1);
    }

    .card-header-custom {
        background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
        color: white;
        padding: 25px 30px;
        font-weight: 700;
        font-size: 1.2rem;
        position: relative;
        overflow: hidden;
        border-radius: 30px 30px 0 0;
        box-shadow:
            0 8px 20px rgba(30, 64, 175, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
    }

    .card-header-custom::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.5s;
    }

    .analysis-card:hover .card-header-custom::before {
        left: 100%;
    }

    /* منطقة الرفع المحسنة بتصميم زجاجي */
    .upload-area {
        border: 3px dashed rgba(59, 130, 246, 0.4);
        border-radius: 25px;
        padding: 60px;
        text-align: center;
        transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        position: relative;
        overflow: hidden;
        box-shadow:
            0 15px 35px rgba(30, 64, 175, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.8);
    }

    .upload-area::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(45deg, rgba(255,255,255,0.1), rgba(255,255,255,0.3));
        opacity: 0;
        transition: opacity 0.3s;
    }

    .upload-area:hover::before {
        opacity: 1;
    }

    .upload-area:hover {
        border-color: #3b82f6;
        background: rgba(255, 255, 255, 1);
        box-shadow:
            0 25px 50px rgba(30, 64, 175, 0.3),
            inset 0 2px 0 rgba(255, 255, 255, 0.9);
        border-color: rgba(59, 130, 246, 0.6);
    }

    .feature-icon {
        font-size: 3rem;
        color: #007bff;
        margin-bottom: 1rem;
    }

    .feature-card {
        background: rgba(255, 255, 255, 0.95);
        border-radius: 20px;
        padding: 25px;
        margin-bottom: 25px;
        box-shadow: 0 10px 25px rgba(30, 64, 175, 0.15);
        transition: all 0.3s ease;
        border: 1px solid rgba(59, 130, 246, 0.2);
    }

    .feature-card:hover {
        transform: translateY(-8px);
        box-shadow: 0 20px 40px rgba(30, 64, 175, 0.25);
        background: rgba(255, 255, 255, 1);
    }

    .btn-analyze {
        background: linear-gradient(45deg, #007bff, #0056b3);
        border: none;
        padding: 12px 30px;
        font-size: 1.1rem;
        font-weight: bold;
        border-radius: 25px;
        transition: all 0.3s ease;
    }

    .btn-analyze:hover {
        box-shadow: 0 4px 12px rgba(0,123,255,0.3);
    }

    .info-badge {
        background: linear-gradient(45deg, #28a745, #20c997);
        color: white;
        padding: 8px 16px;
        border-radius: 20px;
        font-weight: bold;
        margin: 5px;
        display: inline-block;
    }

    /* تصميم الخطوات الصغيرة */
    .step-guide {
        display: flex;
        align-items: center;
        padding: 10px;
        background: #f8f9fa;
        border-radius: 8px;
        border-left: 3px solid #28a745;
        transition: all 0.3s ease;
    }

    .step-guide:hover {
        background: #e8f5e9;
        transform: translateX(3px);
    }

    .step-number-small {
        background: linear-gradient(45deg, #28a745, #20c997);
        color: white;
        width: 25px;
        height: 25px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        font-size: 12px;
        margin-left: 10px;
        flex-shrink: 0;
    }

    .step-text {
        flex: 1;
        font-size: 14px;
    }

    /* أزرار نوع التحليل المحسنة */
    .analysis-type-btn {
        min-width: 220px;
        border-radius: 20px;
        padding: 20px 30px;
        margin: 10px;
        font-weight: 700;
        transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        border: 2px solid rgba(59, 130, 246, 0.3);
        background:
            linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
        color: #1e40af;
        position: relative;
        overflow: hidden;
        text-transform: uppercase;
        letter-spacing: 1px;
        backdrop-filter: blur(20px);
        box-shadow:
            0 10px 25px rgba(30, 64, 175, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
    }

    .analysis-type-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
        transition: left 0.5s;
    }

    .analysis-type-btn:hover::before {
        left: 100%;
    }

    .analysis-type-btn:hover {
        box-shadow:
            0 20px 40px rgba(30, 64, 175, 0.4),
            inset 0 2px 0 rgba(255, 255, 255, 0.3);
        border-color: #3b82f6;
        filter: brightness(1.1);
    }

    .analysis-type-btn.active {
        background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
        color: white;
        border-color: #1e40af;
        box-shadow:
            0 25px 50px rgba(30, 64, 175, 0.5),
            inset 0 2px 0 rgba(255, 255, 255, 0.3);
    }

    .analysis-type-btn.active[data-type="course"] {
        background: linear-gradient(45deg, #007bff, #0056b3);
        border-color: #007bff;
    }

    .analysis-type-btn.active[data-type="data"] {
        background: linear-gradient(45deg, #28a745, #1e7e34);
        border-color: #28a745;
    }

    .analysis-type-btn.active[data-type="evaluation"] {
        background: linear-gradient(45deg, #ffc107, #d39e00);
        border-color: #ffc107;
        color: #212529;
    }

    .btn-group-toggle {
        justify-content: center;
    }

    /* تحسين العنوان الرئيسي بتصميم متطور */
    .page-title {
        color: #1e40af !important;
        font-family: 'Cairo', 'Tajawal', sans-serif !important;
        font-weight: 900;
        font-size: 3rem;
        margin-bottom: 40px;
        text-align: center;
        position: relative;
        text-shadow: 0 2px 4px rgba(30, 64, 175, 0.2);
        letter-spacing: 1px;
    }

    .page-title i {
        margin-left: 15px;
        color: #3b82f6 !important;
    }



    .page-title::after {
        content: '';
        position: absolute;
        bottom: -15px;
        left: 50%;
        transform: translateX(-50%);
        width: 150px;
        height: 6px;
        background: linear-gradient(135deg, #1e40af 0%, #3b82f6 50%, #93c5fd 100%);
        border-radius: 3px;
        box-shadow:
            0 4px 12px rgba(30, 64, 175, 0.5),
            0 0 20px rgba(59, 130, 246, 0.3);
    }



    /* تحسين البطاقات بتصميم زجاجي */
    .feature-card {
        background:
            linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
        border-radius: 25px;
        padding: 35px;
        margin-bottom: 25px;
        box-shadow:
            0 20px 40px rgba(30, 64, 175, 0.15),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
        transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        border: 2px solid rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(20px);

        position: relative;
        overflow: hidden;
    }

    .feature-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
        pointer-events: none;
        opacity: 0.5;
    }

    .feature-card:hover {
        box-shadow:
            0 30px 60px rgba(30, 64, 175, 0.25),
            inset 0 2px 0 rgba(255, 255, 255, 0.3);
        border-color: rgba(255, 255, 255, 0.3);
    }

    /* تحسين الأزرار الرئيسية */
    .btn-analyze {
        background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
        border: none;
        padding: 18px 35px;
        font-size: 1.2rem;
        font-weight: 700;
        border-radius: 20px;
        transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        position: relative;
        overflow: hidden;
        text-transform: uppercase;
        letter-spacing: 1px;
        box-shadow:
            0 15px 30px rgba(30, 64, 175, 0.4),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
        border: 2px solid rgba(59, 130, 246, 0.3);
    }

    .btn-analyze::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.5s;
    }

    .btn-analyze:hover::before {
        left: 100%;
    }

    .btn-analyze:hover {
        box-shadow:
            0 25px 50px rgba(30, 64, 175, 0.5),
            inset 0 2px 0 rgba(255, 255, 255, 0.3);
        border-color: rgba(59, 130, 246, 0.5);
        filter: brightness(1.1);
    }





    .analysis-card:nth-child(2) { animation-delay: 0.1s; }
    .analysis-card:nth-child(3) { animation-delay: 0.2s; }
    .analysis-card:nth-child(4) { animation-delay: 0.3s; }

    /* تحسين النماذج بتصميم زجاجي */
    .form-control, .form-select {
        border-radius: 15px;
        border: 2px solid rgba(59, 130, 246, 0.3);
        padding: 15px 20px;
        transition: all 0.3s ease;
        background:
            linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
        backdrop-filter: blur(15px);
        box-shadow:
            0 5px 15px rgba(30, 64, 175, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
    }

    .form-control:focus, .form-select:focus {
        border-color: #3b82f6;
        box-shadow:
            0 0 0 0.2rem rgba(59, 130, 246, 0.25),
            0 8px 20px rgba(30, 64, 175, 0.2);
        background: rgba(255, 255, 255, 0.9);
        transform: translateY(-2px);
    }

    .form-label {
        font-family: 'Cairo', 'Tajawal', sans-serif !important;
        font-weight: 700;
        color: #1e40af;
        margin-bottom: 12px;
        font-size: 1.1rem;
        text-shadow: 0 1px 2px rgba(30, 64, 175, 0.2);
    }

    /* تحسين العناوين */
    h1, h2, h3, h4, h5, h6 {
        font-family: 'Cairo', 'Tajawal', sans-serif !important;
        font-weight: 700;
        line-height: 1.4;
        color: #1e40af !important;
    }

    /* تحسين النص التوضيحي */
    .lead {
        color: #6c757d !important;
        font-size: 1.1rem;
    }

    /* تحسين النصوص */
    p, span, div, label, button {
        font-family: 'Cairo', 'Tajawal', sans-serif !important;
        line-height: 1.6;
    }

    /* تحسين الأزرار */
    .btn, .analysis-type-btn, .btn-analyze {
        font-family: 'Cairo', 'Tajawal', sans-serif !important;
        font-weight: 600;
        letter-spacing: 0.5px;
    }

    /* تحسين النماذج */
    .form-control, .form-select, input, textarea, select {
        font-family: 'Cairo', 'Tajawal', sans-serif !important;
        font-size: 1rem;
        line-height: 1.5;
    }

    /* تحسين الحاوي الرئيسي بتصميم زجاجي متطور */
    .main-container {
        background: rgba(255, 255, 255, 1);
        backdrop-filter: blur(25px);
        border-radius: 30px;
        padding: 40px;
        margin-top: 25px;
        box-shadow:
            0 30px 60px rgba(30, 64, 175, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
        border: 2px solid rgba(255, 255, 255, 0.2);
        position: relative;
        overflow: hidden;
    }

    .main-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 4px;
        background: #f0f0f0;
    }

    .main-container::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: transparent;
        pointer-events: none;
        opacity: 0.5;
    }

    @media (max-width: 768px) {
        .analysis-type-btn {
            min-width: 100%;
            margin: 5px 0;
        }

        .page-title {
            font-size: 2rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="main-container">
                <!-- Header -->
                <div class="text-center mb-5">
                    <h1 class="page-title">
                        <i class="fas fa-search-plus"></i> تحليل ومقايسة الأسماء الذكي
                    </h1>
                    <p class="lead text-muted">
                        نظام ذكي متطور لتحليل وتصحيح الأسماء العربية ومقايستها مع قاعدة البيانات
                    </p>
                </div>

                <!-- Main Analysis Card -->
                <div class="card analysis-card mb-5">
                    <div class="card-header card-header-custom text-center">
                        <h3 class="mb-0">
                            <i class="fas fa-file-excel"></i> رفع ملف Excel للتحليل الذكي
                        </h3>
                    </div>
                <div class="card-body">
                    <!-- أزرار نوع الفحص -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <h5 class="mb-3 text-center">
                                <i class="fas fa-cogs"></i>
                                نوع الفحص المطلوب:
                            </h5>
                            <div class="btn-group-toggle d-flex flex-wrap justify-content-center gap-3" data-toggle="buttons">
                                <label class="btn btn-outline-primary btn-lg analysis-type-btn active" data-type="course">
                                    <input type="radio" name="analysis_type_radio" value="course" checked>
                                    <i class="fas fa-graduation-cap"></i>
                                    فحص كشف دورة
                                </label>
                                <label class="btn btn-outline-success btn-lg analysis-type-btn" data-type="data">
                                    <input type="radio" name="analysis_type_radio" value="data">
                                    <i class="fas fa-database"></i>
                                    فحص كشف بيانات
                                </label>
                                <label class="btn btn-outline-warning btn-lg analysis-type-btn" data-type="evaluation">
                                    <input type="radio" name="analysis_type_radio" value="evaluation">
                                    <i class="fas fa-star"></i>
                                    فحص كشف تقييمات دورة
                                </label>
                            </div>
                        </div>
                    </div>

                    <form action="{{ url_for('person_data.name_analysis') }}" method="post" enctype="multipart/form-data" id="analysisForm">
                        {% if csrf_token %}
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                        {% endif %}

                        <!-- حقل مخفي لنوع التحليل -->
                        <input type="hidden" name="analysis_type" id="analysis_type_input" value="course">

                        <!-- اختيار الدورة -->
                        <div class="row mb-4">
                            <div class="col-md-6 mx-auto">
                                <div class="card border-info" id="course-selection-card">
                                    <div class="card-header bg-info text-white text-center">
                                        <h5 class="mb-0">
                                            <i class="fas fa-graduation-cap"></i>
                                            <span id="course-card-title">اختيار الدورة (مطلوب)</span>
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-group">
                                            <label for="course_id" class="form-label">
                                                <i class="fas fa-list"></i>
                                                <span id="course-label">اختر دورة لفحص كشف الدورة:</span>
                                            </label>
                                            <select class="form-select" name="course_id" id="course_id" required>
                                                <option value="">-- اختر دورة --</option>
                                                {% for course in courses %}
                                                <option value="{{ course.id }}">
                                                    {{ course.course_number }} - {{ course.title }}
                                                </option>
                                                {% endfor %}
                                            </select>
                                            <div class="form-text text-muted" id="course-help-text">
                                                <i class="fas fa-info-circle"></i>
                                                <span id="course-instruction">يجب اختيار دورة لفحص كشف الدورة</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="upload-area mb-4">
                            <div class="feature-icon">
                                <i class="fas fa-cloud-upload-alt"></i>
                            </div>
                            <h4 class="text-primary mb-3">اختر ملف Excel للتحليل</h4>
                            <input type="file" class="form-control form-control-lg" name="excel_file"
                                   accept=".xlsx,.xls" required style="max-width: 400px; margin: 0 auto;">
                            <p class="text-muted mt-3">
                                <i class="fas fa-info-circle"></i>
                                يجب أن يحتوي الملف على عمود "الاسم الشخصي"
                            </p>
                        </div>

                        <div class="text-center">
                            <button type="submit" class="btn btn-primary btn-analyze" id="analyzeBtn">
                                <i class="fas fa-analytics"></i>
                                <span id="submit-text">بدء تحليل كشف الدورة</span>
                            </button>
                            <a href="{{ url_for('person_data.export_corrections_guide') }}"
                               class="btn btn-success btn-lg ms-3"
                               title="تحميل دليل شامل لجميع التصحيحات المتاحة">
                                <i class="fas fa-book-open"></i> تحميل دليل التصحيحات
                            </a>
                            <a href="{{ url_for('person_data.manage_corrections') }}"
                               class="btn btn-warning btn-lg ms-3"
                               title="إدارة التصحيحات المخصصة">
                                <i class="fas fa-cogs"></i> إدارة التصحيحات
                            </a>

                        </div>

                        <!-- مؤشر التقدم -->
                        <div class="progress mt-3 d-none" id="analysisProgress">
                            <div class="progress-bar progress-bar-striped progress-bar-animated bg-primary"
                                 role="progressbar" style="width: 0%" id="progressBar">
                                <span id="progressText">0%</span>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- دليل التصحيحات -->
            <div class="card mb-5 border-success">
                <div class="card-header bg-success text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-book-open"></i> دليل التصحيحات الشامل
                    </h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <p class="lead">
                                <i class="fas fa-magic text-success"></i>
                                دليل شامل يحتوي على <strong>أكثر من 200 تصحيح</strong> للأسماء العربية الشائعة
                            </p>
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="text-success"><i class="fas fa-spell-check"></i> تصحيحات الهمزات</h6>
                                    <ul class="list-unstyled">
                                        <li><span class="badge bg-danger">احمد</span> → <span class="badge bg-success">أحمد</span></li>
                                        <li><span class="badge bg-danger">ابراهيم</span> → <span class="badge bg-success">إبراهيم</span></li>
                                        <li><span class="badge bg-danger">ايمان</span> → <span class="badge bg-success">إيمان</span></li>
                                        <li class="text-muted">+ أكثر من 80 تصحيح</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="text-success"><i class="fas fa-edit"></i> تصحيحات الألف المقصورة</h6>
                                    <ul class="list-unstyled">
                                        <li><span class="badge bg-danger">عيسي</span> → <span class="badge bg-success">عيسى</span></li>
                                        <li><span class="badge bg-danger">موسي</span> → <span class="badge bg-success">موسى</span></li>
                                        <li><span class="badge bg-danger">على</span> → <span class="badge bg-success">علي</span></li>
                                        <li class="text-muted">+ أكثر من 60 تصحيح</li>
                                    </ul>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-md-6">
                                    <h6 class="text-success"><i class="fas fa-link"></i> الأسماء المركبة</h6>
                                    <ul class="list-unstyled">
                                        <li><span class="badge bg-danger">عبد الله</span> → <span class="badge bg-success">عبدالله</span></li>
                                        <li><span class="badge bg-danger">ابو_الدين</span> → <span class="badge bg-success">أبو الدين</span></li>
                                        <li><span class="badge bg-danger">محمد_علي</span> → <span class="badge bg-success">محمد علي</span></li>
                                        <li class="text-muted">+ أكثر من 50 تصحيح</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="text-success"><i class="fas fa-broom"></i> إزالة الرموز</h6>
                                    <ul class="list-unstyled">
                                        <li><span class="badge bg-danger">علي123</span> → <span class="badge bg-success">علي</span></li>
                                        <li><span class="badge bg-danger">احمد@محمد</span> → <span class="badge bg-success">أحمد محمد</span></li>
                                        <li><span class="badge bg-danger">فاطمة#456</span> → <span class="badge bg-success">فاطمة</span></li>
                                        <li class="text-muted">+ تنظيف شامل</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 text-center">
                            <div class="p-4 bg-light rounded">
                                <i class="fas fa-download fa-3x text-success mb-3"></i>
                                <h5 class="text-success">احصل على الدليل كاملاً</h5>
                                <p class="text-muted">ملف Excel منظم بجميع التصحيحات</p>
                                <a href="{{ url_for('person_data.export_corrections_guide') }}"
                                   class="btn btn-success btn-lg">
                                    <i class="fas fa-file-excel"></i> تحميل الدليل
                                </a>
                                <p class="text-muted mt-2 small">
                                    <i class="fas fa-info-circle"></i>
                                    الملف منسق من اليمين إلى اليسار
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- قسم التصحيحات المخصصة -->
            <div class="card mb-5 border-warning">
                <div class="card-header bg-warning text-dark">
                    <h4 class="mb-0">
                        <i class="fas fa-cogs"></i> التصحيحات المخصصة - ميزة جديدة!
                    </h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <p class="lead">
                                <i class="fas fa-magic text-warning"></i>
                                الآن يمكنك إضافة تصحيحات مخصصة خاصة بك لتحسين دقة النظام!
                            </p>
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="text-warning"><i class="fas fa-plus-circle"></i> إضافة تصحيحات جديدة</h6>
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-check text-success"></i> أضف أي اسم خطأ واسم صحيح</li>
                                        <li><i class="fas fa-check text-success"></i> اختر نوع التصحيح المناسب</li>
                                        <li><i class="fas fa-check text-success"></i> يطبق تلقائياً في التحليلات</li>
                                        <li><i class="fas fa-check text-success"></i> يحفظ في قاعدة البيانات</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="text-warning"><i class="fas fa-chart-line"></i> إحصائيات الاستخدام</h6>
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-check text-success"></i> تتبع عدد مرات الاستخدام</li>
                                        <li><i class="fas fa-check text-success"></i> إمكانية تفعيل/إلغاء التفعيل</li>
                                        <li><i class="fas fa-check text-success"></i> تصدير مع الدليل الشامل</li>
                                        <li><i class="fas fa-check text-success"></i> إدارة كاملة للتصحيحات</li>
                                    </ul>
                                </div>
                            </div>
                            <div class="alert alert-info mt-3">
                                <i class="fas fa-lightbulb"></i>
                                <strong>نصيحة:</strong>
                                ابدأ بإضافة الأسماء الخطأ الشائعة في بياناتك لتحسين دقة التحليل بشكل كبير!
                            </div>
                        </div>
                        <div class="col-md-4 text-center">
                            <div class="p-4 bg-light rounded">
                                <i class="fas fa-tools fa-3x text-warning mb-3"></i>
                                <h5 class="text-warning">ابدأ الآن</h5>
                                <p class="text-muted">أضف تصحيحاتك المخصصة</p>
                                <a href="{{ url_for('person_data.add_correction') }}"
                                   class="btn btn-warning btn-lg mb-2">
                                    <i class="fas fa-plus"></i> إضافة تصحيح
                                </a>
                                <br>
                                <a href="{{ url_for('person_data.manage_corrections') }}"
                                   class="btn btn-outline-warning">
                                    <i class="fas fa-list"></i> إدارة التصحيحات
                                </a>
                                <p class="text-muted mt-2 small">
                                    <i class="fas fa-info-circle"></i>
                                    مرونة كاملة في التحكم
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Features Section -->
            <div class="row mb-5">
                <div class="col-12">
                    <h2 class="text-center text-primary mb-4">
                        <i class="fas fa-star"></i> مميزات النظام
                    </h2>
                </div>

                <div class="col-md-4">
                    <div class="feature-card text-center">
                        <div class="feature-icon">
                            <i class="fas fa-spell-check"></i>
                        </div>
                        <h5 class="text-primary">تصحيح الأسماء العربية</h5>
                        <p class="text-muted">
                            تصحيح تلقائي للأخطاء الشائعة في الأسماء العربية
                            <br>
                            <span class="info-badge">عيسي → عيسى</span>
                            <span class="info-badge">عبداللة → عبدالله</span>
                        </p>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="feature-card text-center">
                        <div class="feature-icon">
                            <i class="fas fa-search"></i>
                        </div>
                        <h5 class="text-primary">مقايسة ذكية</h5>
                        <p class="text-muted">
                            مقايسة الأسماء مع قاعدة البيانات بمستويات مختلفة
                            <br>
                            <span class="info-badge">تشابه ثلاثي</span>
                            <span class="info-badge">تشابه رباعي</span>
                            <span class="info-badge">تشابه خماسي</span>
                        </p>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="feature-card text-center">
                        <div class="feature-icon">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                        <h5 class="text-primary">تقارير مفصلة</h5>
                        <p class="text-muted">
                            إحصائيات شاملة وتقارير قابلة للتصدير
                            <br>
                            <span class="info-badge">إحصائيات</span>
                            <span class="info-badge">تصدير Excel</span>
                        </p>
                    </div>
                </div>
            </div>

            <!-- Advanced Duplicate Detection -->
            <div class="card mb-5 border-warning">
                <div class="card-header bg-warning text-dark">
                    <h4 class="mb-0">
                        <i class="fas fa-shield-alt"></i> فحص التطابق المتقدم - ميزة جديدة!
                    </h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-star"></i>
                        <strong>ميزة جديدة:</strong> النظام الآن يفحص التطابق في البيانات الشخصية لمنع التكرار غير المرغوب
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-danger"><i class="fas fa-ban"></i> منع التطابق في:</h6>
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <i class="fas fa-id-card text-primary"></i>
                                    <strong>الاسم + الرقم الوطني:</strong> منع إضافة نفس الشخص مرتين
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-phone text-success"></i>
                                    <strong>الاسم + رقم الهاتف:</strong> تجنب التكرار بنفس الهاتف
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-shield-alt text-warning"></i>
                                    <strong>الاسم + الرقم العسكري:</strong> منع تكرار الأرقام العسكرية
                                </li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-success"><i class="fas fa-check-circle"></i> السماح بـ:</h6>
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <i class="fas fa-users text-info"></i>
                                    <strong>أسماء مكررة:</strong> إذا اختلفت البيانات الشخصية
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-family text-primary"></i>
                                    <strong>مثال:</strong> محمد أحمد (رقم وطني: 123) ومحمد أحمد (رقم وطني: 456)
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-lightbulb text-warning"></i>
                                    <strong>ذكي:</strong> يميز بين الأشخاص المختلفين بنفس الاسم
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div class="alert alert-success mt-3">
                        <i class="fas fa-info-circle"></i>
                        <strong>ملاحظة:</strong> لاستخدام هذه الميزة، تأكد من وجود أعمدة "الرقم الوطني" أو "رقم الهاتف" أو "الرقم العسكري" في ملف Excel
                    </div>
                </div>
            </div>

            <!-- Analysis Types -->
            <div class="card mb-5">
                <div class="card-header bg-info text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-list-alt"></i> أنواع التحليل المتاحة
                    </h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary"><i class="fas fa-check-circle"></i> المطابقات التامة</h6>
                            <p class="text-muted">الأسماء المطابقة تماماً مع قاعدة البيانات</p>

                            <h6 class="text-primary"><i class="fas fa-edit"></i> الأسماء المصححة</h6>
                            <p class="text-muted">الأسماء التي تم تصحيحها تلقائياً</p>

                            <h6 class="text-success"><i class="fas fa-plus-circle"></i> السجلات الجديدة</h6>
                            <p class="text-muted">السجلات غير الموجودة في قاعدة البيانات</p>

                            <h6 class="text-warning"><i class="fas fa-exclamation-triangle"></i> السجلات المرفوضة</h6>
                            <p class="text-muted">سجلات مرفوضة بسبب التطابق في البيانات</p>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-primary"><i class="fas fa-layer-group"></i> التشابه الثلاثي</h6>
                            <p class="text-muted">أسماء تتشارك في 3 أجزاء</p>

                            <h6 class="text-primary"><i class="fas fa-layer-group"></i> التشابه الرباعي</h6>
                            <p class="text-muted">أسماء تتشارك في 4 أجزاء</p>

                            <h6 class="text-primary"><i class="fas fa-layer-group"></i> التشابه الخماسي</h6>
                            <p class="text-muted">أسماء تتشارك في 5 أجزاء أو أكثر</p>

                            <h6 class="text-success"><i class="fas fa-check-double"></i> الأسماء المكررة المسموحة</h6>
                            <p class="text-muted">أسماء مكررة لكن بيانات شخصية مختلفة</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- دليل الاستخدام المفصل -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card border-success">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-book-open"></i> دليل الاستخدام المفصل
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="text-primary mb-3">
                                        <i class="fas fa-clipboard-list"></i> خطوات التحليل:
                                    </h6>
                                    <div class="step-guide mb-3">
                                        <div class="step-number-small">1</div>
                                        <div class="step-text">
                                            <strong>تحضير الملف:</strong> تأكد من وجود عمود "الاسم الشخصي"
                                        </div>
                                    </div>
                                    <div class="step-guide mb-3">
                                        <div class="step-number-small">2</div>
                                        <div class="step-text">
                                            <strong>رفع الملف:</strong> اختر ملف Excel واضغط "بدء التحليل"
                                        </div>
                                    </div>
                                    <div class="step-guide mb-3">
                                        <div class="step-number-small">3</div>
                                        <div class="step-text">
                                            <strong>انتظار النتائج:</strong> سيتم التحليل تلقائياً
                                        </div>
                                    </div>
                                    <div class="step-guide mb-3">
                                        <div class="step-number-small">4</div>
                                        <div class="step-text">
                                            <strong>مراجعة التقرير:</strong> راجع الإحصائيات والنتائج
                                        </div>
                                    </div>
                                    <div class="step-guide">
                                        <div class="step-number-small">5</div>
                                        <div class="step-text">
                                            <strong>التصدير:</strong> احفظ النتائج أو استورد البيانات
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <h6 class="text-warning mb-3">
                                        <i class="fas fa-magic"></i> ما سيحدث أثناء التحليل:
                                    </h6>
                                    <ul class="list-unstyled">
                                        <li class="mb-2">
                                            <i class="fas fa-check-circle text-success"></i>
                                            <strong>تصحيح الأسماء:</strong> عيسي → عيسى، احمد → أحمد
                                        </li>
                                        <li class="mb-2">
                                            <i class="fas fa-search text-info"></i>
                                            <strong>البحث عن التكرارات:</strong> مقايسة مع قاعدة البيانات
                                        </li>
                                        <li class="mb-2">
                                            <i class="fas fa-layer-group text-primary"></i>
                                            <strong>تحليل التشابه:</strong> ثلاثي، رباعي، خماسي
                                        </li>
                                        <li class="mb-2">
                                            <i class="fas fa-plus-circle text-success"></i>
                                            <strong>كشف الأسماء الجديدة:</strong> غير موجودة في النظام
                                        </li>
                                        <li class="mb-2">
                                            <i class="fas fa-chart-bar text-warning"></i>
                                            <strong>إحصائيات شاملة:</strong> تقرير مفصل بالأرقام
                                        </li>
                                    </ul>

                                    <div class="alert alert-warning mt-3">
                                        <i class="fas fa-lightbulb"></i>
                                        <strong>نصيحة:</strong> كلما كانت الأسماء أكثر تنظيماً في ملف Excel، كانت النتائج أدق
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- تعليمات سريعة -->
            <div class="alert alert-info">
                <h5><i class="fas fa-rocket"></i> البدء السريع:</h5>
                <div class="row">
                    <div class="col-md-8">
                        <ol class="mb-0">
                            <li><strong>ارفع ملف Excel</strong> يحتوي على عمود "الاسم الشخصي"</li>
                            <li><strong>اضغط "بدء التحليل"</strong> وانتظر النتائج</li>
                            <li><strong>راجع التقرير</strong> واختر نوع التصدير المناسب</li>
                        </ol>
                    </div>
                    <div class="col-md-4 text-center">
                        <i class="fas fa-clock text-primary fa-2x mb-2"></i>
                        <br>
                        <small class="text-muted">عادة يستغرق التحليل أقل من دقيقة</small>
                    </div>
                </div>
            </div>
            </div> <!-- إغلاق main-container -->
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // تعريف إعدادات كل نوع فحص
        const analysisTypes = {
            course: {
                cardTitle: 'اختيار الدورة (مطلوب)',
                label: 'اختر دورة لفحص كشف الدورة:',
                submitText: 'بدء تحليل كشف الدورة',
                required: true,
                instruction: 'يجب اختيار دورة لفحص كشف الدورة',
                placeholder: '-- اختر دورة --'
            },
            data: {
                cardTitle: 'اختيار الدورة (اختياري)',
                label: 'اختر دورة لإضافة الأسماء الجديدة إليها (اختياري):',
                submitText: 'بدء تحليل البيانات الشخصية',
                required: false,
                instruction: 'اختيار الدورة اختياري لفحص البيانات العامة',
                placeholder: '-- لا تضيف لأي دورة (اختياري) --'
            },
            evaluation: {
                cardTitle: 'اختيار الدورة (مطلوب)',
                label: 'اختر دورة لفحص كشف تقييمات الدورة:',
                submitText: 'بدء تحليل كشف التقييمات',
                required: true,
                instruction: 'يجب اختيار دورة لفحص كشف تقييمات الدورة',
                placeholder: '-- اختر دورة --'
            }
        };

        // دالة تحديث الواجهة حسب نوع الفحص
        function updateInterface(type) {
            const config = analysisTypes[type];

            // تحديث عنوان بطاقة الدورة
            $('#course-card-title').text(config.cardTitle);

            // تحديث تسمية اختيار الدورة
            $('#course-label').text(config.label);

            // تحديث نص زر الإرسال
            $('#submit-text').text(config.submitText);

            // تحديث حالة اختيار الدورة (مطلوب أم لا)
            $('#course_id').prop('required', config.required);

            // تحديث النص التوضيحي
            $('#course-instruction').text(config.instruction);

            // تحديث placeholder
            $('#course_id option:first').text(config.placeholder);

            // إذا لم يكن مطلوب، امسح الاختيار
            if (!config.required) {
                $('#course_id').val('');
            }

            // تحديث قيمة نوع التحليل المخفية
            $('#analysis_type_input').val(type);



            // تحديث لون بطاقة الدورة
            const cardHeader = $('#course-selection-card .card-header');
            cardHeader.removeClass('bg-info bg-success bg-warning');

            if (type === 'course') {
                cardHeader.addClass('bg-info');
            } else if (type === 'data') {
                cardHeader.addClass('bg-success');
            } else if (type === 'evaluation') {
                cardHeader.addClass('bg-warning text-dark');
            }
        }

        // إضافة مستمعي الأحداث لأزرار نوع الفحص
        $('.analysis-type-btn').on('click', function() {
            // إزالة الفئة النشطة من جميع الأزرار
            $('.analysis-type-btn').removeClass('active');

            // إضافة الفئة النشطة للزر المحدد
            $(this).addClass('active');

            // تحديث الواجهة
            const type = $(this).data('type');
            updateInterface(type);
        });

        // تحديث الواجهة عند التحميل (الافتراضي: فحص دورة)
        updateInterface('course');

        // تحسين تجربة رفع الملف
        $('input[type="file"]').change(function() {
            var fileName = $(this).val().split('\\').pop();
            if (fileName) {
                $('.upload-area h4').text('تم اختيار: ' + fileName);
                $('.upload-area').addClass('border-success').removeClass('border-primary');
                $('.feature-icon i').removeClass('fa-cloud-upload-alt').addClass('fa-check-circle text-success');
            }
        });

        // تأثيرات بصرية للنموذج
        $('#analysisForm').on('submit', function(e) {
            console.log('🔥 تم إرسال النموذج');

            // التحقق من وجود ملف
            var fileInput = $('input[name="excel_file"]')[0];
            if (!fileInput.files || fileInput.files.length === 0) {
                alert('يرجى اختيار ملف Excel أولاً');
                e.preventDefault();
                return false;
            }

            // التحقق من اختيار الدورة إذا كان مطلوب
            const analysisType = $('#analysis_type_input').val();
            const courseRequired = analysisTypes[analysisType].required;
            const courseSelected = $('#course_id').val();

            if (courseRequired && !courseSelected) {
                alert('يرجى اختيار دورة أولاً');
                e.preventDefault();
                return false;
            }

            console.log('📁 الملف المختار:', fileInput.files[0].name);
            console.log('🎯 نوع التحليل:', analysisType);
            console.log('🎓 الدورة المختارة:', courseSelected || 'لا توجد');

            $('.btn-analyze').html('<i class="fas fa-spinner fa-spin"></i> جاري التحليل...');
            $('.btn-analyze').prop('disabled', true);

            // إظهار مؤشر التقدم
            $('#analysisProgress').removeClass('d-none');

            // محاكاة تقدم التحليل
            var progress = 0;
            var interval = setInterval(function() {
                progress += Math.random() * 15;
                if (progress > 90) progress = 90;

                $('#progressBar').css('width', progress + '%');
                $('#progressText').text(Math.round(progress) + '%');

                if (progress >= 90) {
                    clearInterval(interval);
                    $('#progressText').text('جاري إنهاء التحليل...');
                }
            }, 500);
        });
    });
</script>
{% endblock %}
