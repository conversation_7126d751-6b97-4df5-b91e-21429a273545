/* خلفية رسمية متحركة للصفحة */
body {
    background:
        linear-gradient(135deg, #1e3a8a 0%, #3b82f6 25%, #60a5fa 50%, #93c5fd 75%, #dbeafe 100%);
    background-size: 400% 400%;
    animation: gradientShift 20s ease infinite;
    min-height: 100vh;
    position: relative;
    overflow-x: hidden;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(30, 58, 138, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(59, 130, 246, 0.2) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(147, 197, 253, 0.2) 0%, transparent 50%),
        radial-gradient(circle at 60% 70%, rgba(219, 234, 254, 0.1) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
    animation: floatingBubbles 25s ease-in-out infinite;
}

@keyframes floatingBubbles {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-15px) rotate(120deg); }
    66% { transform: translateY(8px) rotate(240deg); }
}

/* تحسين الشريط الجانبي بتصميم زجاجي */
.sidebar {
    background:
        linear-gradient(135deg, rgba(30, 58, 138, 0.95) 0%, rgba(30, 64, 175, 0.95) 50%, rgba(59, 130, 246, 0.95) 100%);
    color: white;
    min-height: calc(100vh - 56px);
    padding-top: 35px;
    border-radius: 25px;
    box-shadow:
        0 25px 50px rgba(30, 58, 138, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(20px);
    border: 2px solid rgba(255, 255, 255, 0.15);
    position: relative;
    overflow: hidden;
}

.sidebar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
    pointer-events: none;
}

/* تصميم صورة المستخدم الافتراضية */
.user-avatar {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px;
    box-shadow:
        0 15px 30px rgba(30, 64, 175, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    border: 3px solid rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.user-avatar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 50%);
    border-radius: 50%;
}

.user-avatar i {
    font-size: 2.5rem;
    color: white;
    z-index: 2;
    position: relative;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.user-avatar:hover {
    transform: scale(1.05);
    box-shadow:
        0 20px 40px rgba(30, 64, 175, 0.5),
        inset 0 1px 0 rgba(255, 255, 255, 0.4);
    border-color: rgba(255, 255, 255, 0.3);
}

.sidebar-link {
    color: rgba(255, 255, 255, 0.8);
    padding: 15px 20px;
    display: block;
    text-decoration: none;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    border-radius: 15px;
    margin: 8px 15px;
    position: relative;
    overflow: hidden;
    font-weight: 500;
}

.sidebar-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.sidebar-link:hover::before {
    left: 100%;
}

.sidebar-link:hover, .sidebar-link.active {
    color: white;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.4) 0%, rgba(147, 197, 253, 0.3) 100%);
    transform: translateX(8px);
    box-shadow:
        0 8px 20px rgba(59, 130, 246, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.sidebar-link i {
    margin-left: 10px;
    width: 20px;
    text-align: center;
}

/* تحسين الحاوي الرئيسي بتصميم زجاجي متطور */
.main-container {
    background:
        linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    backdrop-filter: blur(25px);
    border-radius: 30px;
    padding: 40px;
    margin-top: 25px;
    box-shadow:
        0 30px 60px rgba(30, 64, 175, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
}

.main-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, #1e40af, #3b82f6, #60a5fa, #93c5fd);
    background-size: 300% 100%;
    animation: shimmer 4s ease-in-out infinite;
}

.main-container::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
    pointer-events: none;
    opacity: 0.5;
}

@keyframes shimmer {
    0% { background-position: -300% 0; }
    100% { background-position: 300% 0; }
}

@keyframes rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* تحسين العناوين */
.page-title {
    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 900;
    font-size: 2.5rem;
    filter: drop-shadow(0 4px 8px rgba(30, 64, 175, 0.4));
    margin-bottom: 30px;
}

.page-title i {
    margin-left: 15px;
    animation: rotate 4s linear infinite;
    filter: drop-shadow(0 0 10px rgba(30, 64, 175, 0.5));
}

/* تحسين النماذج */
.form-label {
    color: #1e40af;
    font-weight: 600;
    margin-bottom: 8px;
}

.form-control, .form-select {
    border: 2px solid rgba(59, 130, 246, 0.2);
    border-radius: 15px;
    padding: 12px 15px;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 0.2rem rgba(59, 130, 246, 0.25);
    background: rgba(255, 255, 255, 1);
}

/* تحسين الأزرار */
.btn {
    border-radius: 15px;
    padding: 12px 20px;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}



/* تحسين قسم الفلترة */
.filter-section {
    background:
        linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    backdrop-filter: blur(20px);
    padding: 30px;
    border-radius: 25px;
    margin-bottom: 30px;
    border: 2px solid rgba(255, 255, 255, 0.2);
    box-shadow:
        0 20px 40px rgba(30, 64, 175, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

/* تحسين التنبيهات */
.alert {
    border-radius: 20px;
    border: none;
    backdrop-filter: blur(10px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

/* تحسين الترقيم */
.pagination .page-link {
    border-radius: 15px;
    margin: 0 5px;
    border: 2px solid rgba(59, 130, 246, 0.2);
    color: #1e40af;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.pagination .page-link:hover {
    background: #3b82f6;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(59, 130, 246, 0.3);
}

.pagination .page-item.active .page-link {
    background: #1e40af;
    border-color: #1e40af;
    color: white;
}

/* تحسين شريط التنقل العلوي */
.navbar {
    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 50%, #60a5fa 100%) !important;
    backdrop-filter: blur(20px);
    border-bottom: 2px solid rgba(255, 255, 255, 0.2);
    box-shadow:
        0 8px 32px rgba(30, 64, 175, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    padding: 15px 0;
    position: relative;
    overflow: hidden;
}

.navbar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
    pointer-events: none;
}

.navbar-brand {
    font-weight: 900 !important;
    font-size: 1.5rem !important;
    color: white !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    position: relative;
    z-index: 2;
    transition: all 0.3s ease;
}

.navbar-brand:hover {
    color: rgba(255, 255, 255, 0.9) !important;
    transform: scale(1.05);
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
}

.navbar-brand i {
    color: #fbbf24;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.nav-link {
    color: rgba(255, 255, 255, 0.9) !important;
    font-weight: 600 !important;
    padding: 12px 20px !important;
    border-radius: 15px !important;
    margin: 0 5px !important;
    transition: all 0.3s ease !important;
    position: relative;
    z-index: 2;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.nav-link:hover {
    color: white !important;
    background: rgba(255, 255, 255, 0.2) !important;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.nav-link.active {
    color: white !important;
    background: rgba(255, 255, 255, 0.25) !important;
    box-shadow:
        0 5px 15px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.dropdown-menu {
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(20px) !important;
    border: 2px solid rgba(59, 130, 246, 0.2) !important;
    border-radius: 20px !important;
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.8) !important;
    padding: 15px 0 !important;
    margin-top: 10px !important;
}

.dropdown-item {
    color: #1e40af !important;
    font-weight: 600 !important;
    padding: 12px 25px !important;
    border-radius: 15px !important;
    margin: 5px 15px !important;
    transition: all 0.3s ease !important;
}

.dropdown-item:hover {
    background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%) !important;
    color: white !important;
    transform: translateX(5px);
}

.dropdown-divider {
    border-color: rgba(59, 130, 246, 0.2) !important;
    margin: 10px 15px !important;
}

.navbar-toggler {
    border: 2px solid rgba(255, 255, 255, 0.3) !important;
    border-radius: 15px !important;
    padding: 8px 12px !important;
}

.navbar-toggler:focus {
    box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25) !important;
}

.navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.9%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='m4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e") !important;
}


