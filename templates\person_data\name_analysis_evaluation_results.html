<!-- نقطة التراجع الأولى: تم إنشاؤها بعد إكمال الرسوم البيانية وتصدير Excel لصفحة تحليل كشف الدورة -->
{% extends "layout.html" %}

{% block head %}
<meta name="csrf-token" content="{{ csrf_token() }}">
{% endblock %}

{% block styles %}
<link rel="stylesheet" href="{{ url_for('static', filename='libs/bootstrap/bootstrap.rtl.min.css') }}">
<link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
<style>
    /* تطبيق الخط الجديد على العناوين والنصوص المهمة فقط */
    h1, h2, h3, h4, h5, h6 {
        font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .card-title, .card-header, .alert {
        font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }
    .stats-card {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        color: white;
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 10px 20px rgba(0, 123, 255, 0.1);
        transition: all 0.3s ease;
        border: 1px solid rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
    }

    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 30px rgba(0, 123, 255, 0.2);
        background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
    }

    .stat-number {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 5px;
    }

    .stat-label {
        font-size: 0.9rem;
        opacity: 0.9;
    }

    .result-section {
        background: white;
        border-radius: 15px;
        padding: 0;
        margin-bottom: 25px;
        box-shadow: 0 10px 25px rgba(0, 123, 255, 0.1);
        border: 1px solid rgba(0, 123, 255, 0.1);
        overflow: hidden;
    }

    .name-item {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 15px;
        border: 1px solid rgba(0, 123, 255, 0.1);
        box-shadow: 0 4px 8px rgba(0, 123, 255, 0.05);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .name-item::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        width: 4px;
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    }

    .name-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 16px rgba(0, 123, 255, 0.1);
    }

    .corrected-name::before {
        background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
    }

    .similar-name::before {
        background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    }

    .new-name::before {
        background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
    }

    .btn-export {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        border: none;
        padding: 12px 30px;
        font-size: 1.1rem;
        font-weight: bold;
        border-radius: 25px;
        color: white;
        transition: all 0.3s ease;
        font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
    }

    .btn-export:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 123, 255, 0.4);
        color: white;
        background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
    }

    .btn-export-secondary {
        background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
        border: none;
        padding: 10px 25px;
        font-size: 1rem;
        font-weight: 600;
        border-radius: 20px;
        color: white;
        transition: all 0.3s ease;
        font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        box-shadow: 0 3px 10px rgba(40, 167, 69, 0.3);
    }

    .btn-export-secondary:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
        color: white;
        background: linear-gradient(135deg, #1e7e34 0%, #155724 100%);
    }

    .btn-export-secondary:focus {
        box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
        color: white;
    }

    .section-header {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        color: white;
        padding: 20px;
        margin-bottom: 0;
        position: relative;
        overflow: hidden;
    }

    .section-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 100%);
        pointer-events: none;
    }

    .badge-custom {
        font-size: 0.8rem;
        padding: 5px 10px;
        border-radius: 15px;
    }

    .progress-custom {
        height: 8px;
        border-radius: 10px;
        background-color: #e9ecef;
    }

    .table-responsive {
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .evaluation-header {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        color: white;
        padding: 25px;
        border-radius: 15px;
        margin-bottom: 25px;
        box-shadow: 0 10px 25px rgba(0, 123, 255, 0.2);
        position: relative;
        overflow: hidden;
    }

    .evaluation-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 100%);
        pointer-events: none;
    }

    .evaluation-stats {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    }

    /* تحسين البطاقات */
    .card {
        border-radius: 15px;
        box-shadow: 0 8px 20px rgba(0, 123, 255, 0.1);
        border: 1px solid rgba(0, 123, 255, 0.1);
        transition: all 0.3s ease;
    }

    .card:hover {
        transform: translateY(-3px);
        box-shadow: 0 12px 30px rgba(0, 123, 255, 0.15);
    }

    .card-header {
        border-radius: 15px 15px 0 0 !important;
        border-bottom: 1px solid rgba(0, 123, 255, 0.1);
    }

    .card-body {
        border-radius: 0 0 15px 15px;
    }

    /* تحسين التنبيهات */
    .alert {
        border-radius: 12px;
        border: none;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        position: relative;
        overflow: hidden;
    }

    .alert::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        width: 4px;
        background: currentColor;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="text-danger mb-2">
                        <i class="fas fa-star"></i> نتائج تحليل كشف التقييمات
                    </h1>
                    <p class="text-muted">
                        <i class="fas fa-file-excel"></i> الملف: {{ excel_filename }}
                    </p>
                    {% if selected_course %}
                    <div class="alert alert-warning">
                        <i class="fas fa-graduation-cap"></i>
                        <strong>الدورة المختارة:</strong> {{ selected_course.course_number }} - {{ selected_course.title }}
                    </div>
                    {% endif %}
                </div>
                <div>
                    <a href="{{ url_for('person_data.export_analysis_results') }}" class="btn btn-export me-2" title="تصدير تقرير شامل يحتوي على: لديهم تقييم، ليس لديهم تقييم، تقييمات جديدة، تقييمات مرفوضة، وجميع التفاصيل">
                        <i class="fas fa-download"></i> تصدير تقرير التقييمات الكامل
                    </a>
                    <a href="{{ url_for('person_data.export_new_names_only') }}" class="btn btn-export-secondary me-2">
                        <i class="fas fa-file-excel"></i> تصدير التقييمات الجديدة فقط
                    </a>
                    {% if results.new_records or results.allowed_duplicates or results.corrected_names %}
                    <form method="post" action="{{ url_for('person_data.import_analyzed_names') }}" class="d-inline">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                        {% if selected_course %}
                        <button type="submit" class="btn btn-export-secondary me-2"
                                onclick="return confirm('هل تريد إضافة {{ (results.new_records|length if results.new_records else 0) + (results.allowed_duplicates|length if results.allowed_duplicates else 0) }} تقييم جديد إلى قاعدة البيانات والدورة {{ selected_course.course_number }}؟')">
                            <i class="fas fa-database"></i> إضافة للقاعدة والدورة
                        </button>
                        {% else %}
                        <button type="submit" class="btn btn-export-secondary me-2"
                                onclick="return confirm('هل تريد إضافة {{ (results.new_records|length if results.new_records else 0) + (results.allowed_duplicates|length if results.allowed_duplicates else 0) }} تقييم جديد إلى قاعدة البيانات؟')">
                            <i class="fas fa-database"></i> إضافة التقييمات الجديدة
                        </button>
                        {% endif %}
                    </form>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stats-card text-center">
                <div class="stat-number">{{ results.statistics.total_processed }}</div>
                <div class="stat-label">إجمالي التقييمات المعالجة</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card text-center">
                <div class="stat-number">{{ results.statistics.corrected_count }}</div>
                <div class="stat-label">الأسماء المصححة</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card text-center">
                <div class="stat-number">{{ results.statistics.exact_matches_count }}</div>
                <div class="stat-label">موجود في قاعدة البيانات</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card text-center">
                <div class="stat-number">{{ results.statistics.new_records_count }}</div>
                <div class="stat-label">غير موجود في قاعدة البيانات</div>
            </div>
        </div>
    </div>

    <!-- Advanced Duplicate Detection Cards -->
    {% if results.statistics.has_national_id_column or results.statistics.has_phone_column or results.statistics.has_military_id_column %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-warning">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-search"></i> نتائج فحص التطابق المتقدم للتقييمات
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-2">
                            <div class="card text-center border-danger">
                                <div class="card-body">
                                    <h5 class="text-danger">{{ results.statistics.blocked_duplicates_count }}</h5>
                                    <small class="text-muted">تقييمات مرفوضة</small>
                                    <br><small class="text-danger">تطابق في البيانات</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="card text-center border-success">
                                <div class="card-body">
                                    <h5 class="text-success">{{ results.statistics.allowed_duplicates_count }}</h5>
                                    <small class="text-muted">أسماء مكررة مسموحة</small>
                                    <br><small class="text-success">بيانات مختلفة</small>
                                </div>
                            </div>
                        </div>
                        {% if results.statistics.has_national_id_column %}
                        <div class="col-md-2">
                            <div class="card text-center border-info">
                                <div class="card-body">
                                    <h5 class="text-info">{{ results.statistics.name_national_id_matches + results.statistics.national_id_only_matches }}</h5>
                                    <small class="text-muted">تطابق رقم وطني</small>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                        {% if results.statistics.has_phone_column %}
                        <div class="col-md-2">
                            <div class="card text-center border-info">
                                <div class="card-body">
                                    <h5 class="text-info">{{ results.statistics.name_phone_matches + results.statistics.phone_only_matches }}</h5>
                                    <small class="text-muted">تطابق رقم هاتف</small>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                        {% if results.statistics.has_military_id_column %}
                        <div class="col-md-2">
                            <div class="card text-center border-info">
                                <div class="card-body">
                                    <h5 class="text-info">{{ results.statistics.name_military_id_matches + results.statistics.military_id_only_matches }}</h5>
                                    <small class="text-muted">تطابق رقم عسكري</small>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    {% if results.course_analysis and results.course_analysis.selected_course %}
    <!-- تحليل التقييمات للدورة المختارة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-warning">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-star"></i> تحليل التقييمات للدورة
                    </h5>
                </div>
                <div class="card-body">
                    <!-- معلومات الدورة -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <h6 class="text-warning">معلومات الدورة:</h6>
                            <p class="mb-1"><strong>رقم الدورة:</strong> {{ results.course_analysis.selected_course.course_number }}</p>
                            <p class="mb-1"><strong>اسم الدورة:</strong> {{ results.course_analysis.selected_course.title }}</p>
                            <p class="mb-1"><strong>الجهة:</strong> {{ results.course_analysis.selected_course.agency or 'غير محدد' }}</p>
                            <p class="mb-0"><strong>المركز:</strong> {{ results.course_analysis.selected_course.center_name or 'غير محدد' }}</p>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-success">إحصائيات التقييمات:</h6>
                            {% if results.course_analysis.participants_summary %}
                            <p class="mb-1"><strong>التقييمات الحالية:</strong>
                                <span class="badge bg-info">{{ results.course_analysis.participants_summary.current_participants_count }}</span>
                            </p>
                            <p class="mb-1"><strong>التقييمات الجديدة:</strong>
                                <span class="badge bg-success">{{ results.course_analysis.participants_summary.new_participants_count }}</span>
                            </p>
                            <p class="mb-1"><strong>التقييمات المكررة:</strong>
                                <span class="badge bg-warning">{{ results.course_analysis.participants_summary.duplicate_participants_count }}</span>
                            </p>
                            <p class="mb-0"><strong>إجمالي بعد الاستيراد:</strong>
                                <span class="badge bg-primary">{{ results.course_analysis.participants_summary.total_after_import }}</span>
                            </p>
                            {% endif %}
                        </div>
                    </div>

                    <!-- تحذيرات التقييمات المكررة -->
                    {% if results.course_analysis.duplicate_participants %}
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>تحذير:</strong> تم العثور على {{ results.course_analysis.duplicate_participants|length }} تقييم مكرر في كشف التقييمات.
                        هذه التقييمات موجودة مسبقاً في الدورة ولن يتم إضافتها مرة أخرى.
                    </div>

                    <!-- عرض التقييمات المكررة -->
                    <div class="mb-3">
                        <h6 class="text-warning">التقييمات المكررة في الدورة:</h6>
                        <div class="row">
                            {% for duplicate in results.course_analysis.duplicate_participants[:5] %}
                            <div class="col-md-6 mb-2">
                                <div class="card border-warning">
                                    <div class="card-body p-2">
                                        <h6 class="card-title text-warning mb-1">{{ duplicate.name }}</h6>
                                        <small class="text-muted">{{ duplicate.reason }}</small>
                                        <br><small class="badge bg-warning">موجود في الصف {{ duplicate.excel_record.row_index }}</small>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        {% if results.course_analysis.duplicate_participants|length > 5 %}
                        <p class="text-muted text-center mt-2">
                            <i class="fas fa-info-circle"></i>
                            يتم عرض أول 5 تقييمات مكررة فقط. للاطلاع على القائمة الكاملة، قم بتصدير النتائج.
                        </p>
                        {% endif %}
                    </div>
                    {% endif %}

                    <!-- التقييمات الجديدة للدورة -->
                    {% if results.course_analysis.new_participants %}
                    <div class="mb-3">
                        <h6 class="text-success">التقييمات الجديدة التي سيتم إضافتها للدورة:</h6>
                        <div class="row">
                            {% for new_evaluation in results.course_analysis.new_participants[:5] %}
                            <div class="col-md-6 mb-2">
                                <div class="card border-success">
                                    <div class="card-body p-2">
                                        <h6 class="card-title text-success mb-1">{{ new_evaluation.name }}</h6>
                                        <small class="badge bg-success">{{ new_evaluation.type }}</small>
                                        <br><small class="text-muted">من الصف {{ new_evaluation.excel_record.row_index }}</small>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        {% if results.course_analysis.new_participants|length > 5 %}
                        <p class="text-muted text-center mt-2">
                            <i class="fas fa-info-circle"></i>
                            يتم عرض أول 5 تقييمات جديدة فقط. للاطلاع على القائمة الكاملة، قم بتصدير النتائج.
                        </p>
                        {% endif %}
                    </div>
                    {% endif %}

                    <!-- زر تحديث التقييمات المدخلة -->
                    <div class="text-center mt-4">
                        {% if results.exact_matches %}
                        <button type="button" class="btn btn-info btn-lg me-3" data-bs-toggle="modal" data-bs-target="#updateEvaluationsModal">
                            <i class="fas fa-edit"></i> تحديث التقييمات المدخلة ({{ results.exact_matches|length }})
                        </button>
                        {% endif %}
                        {% if results.course_analysis.new_participants %}
                        <form method="post" action="{{ url_for('person_data.import_analyzed_names') }}" class="d-inline">
                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                            <button type="submit" class="btn btn-success btn-lg"
                                    onclick="return confirm('هل تريد إضافة {{ results.course_analysis.new_participants|length }} تقييم جديد للدورة وقاعدة البيانات؟')">
                                <i class="fas fa-plus-circle"></i> إضافة التقييمات الجديدة
                            </button>
                        </form>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Corrected Names Section -->
    {% if results.corrected_names %}
    <div class="result-section">
        <div class="section-header">
            <h4 class="mb-0">
                <i class="fas fa-spell-check"></i> الأسماء المصححة في التقييمات
                <span class="badge bg-warning">{{ results.corrected_names|length }}</span>
            </h4>
        </div>
        <div class="p-3">
            <div class="row">
                {% for correction in results.corrected_names[:10] %}
                <div class="col-md-6 mb-2">
                    <div class="name-item corrected-name">
                        <strong>الأصلي:</strong> {{ correction.original }}<br>
                        <strong>المصحح:</strong> <span class="text-success">{{ correction.corrected }}</span>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% if results.corrected_names|length > 10 %}
            <p class="text-muted text-center mt-3">
                <i class="fas fa-info-circle"></i>
                يتم عرض أول 10 أسماء فقط. للاطلاع على جميع النتائج، قم بتصدير الملف.
            </p>
            {% endif %}
        </div>
    </div>
    {% endif %}

    <!-- Exact Matches Section - موجود في قاعدة البيانات -->
    {% if results.exact_matches %}
    <div class="result-section">
        <div class="section-header">
            <h4 class="mb-0">
                <i class="fas fa-user-check"></i> موجود في قاعدة البيانات - يحتاج تحديث تقييم
                <span class="badge bg-success">{{ results.exact_matches|length }}</span>
            </h4>
        </div>
        <div class="p-3">
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i>
                <strong>ملاحظة:</strong> هؤلاء الأشخاص موجودين في قاعدة البيانات ولديهم تقييمات مدخلة مسبقاً. يمكنك تحديث تقييماتهم أو إضافة تقييمات جديدة لهم.
            </div>
            <div class="row mb-3">
                <div class="col-md-6">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>تحذير:</strong> تحديث التقييمات سيؤثر على البيانات الموجودة
                    </div>
                </div>
                <div class="col-md-6">
                    <button type="button" class="btn btn-warning btn-lg w-100" data-bs-toggle="modal" data-bs-target="#updateEvaluationsModal">
                        <i class="fas fa-edit"></i> تحديث التقييمات المدخلة
                    </button>
                </div>
            </div>
            <div class="row">
                {% for match in results.exact_matches[:10] %}
                <div class="col-md-12 mb-3">
                    <div class="card border-success">
                        <div class="card-body p-3">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="text-success mb-2">
                                        <i class="fas fa-star"></i> من ملف التقييمات:
                                    </h6>
                                    <p class="mb-1"><strong>{{ match.excel_name }}</strong></p>
                                    <small class="text-muted">الصف: {{ match.excel_record.row_index }}</small>
                                    {% if match.excel_record.national_id %}
                                    <br><small class="badge bg-info">رقم وطني: {{ match.excel_record.national_id }}</small>
                                    {% endif %}
                                    {% if match.excel_record.phone %}
                                    <br><small class="badge bg-success">هاتف: {{ match.excel_record.phone }}</small>
                                    {% endif %}
                                    {% if match.excel_record.military_id %}
                                    <br><small class="badge bg-warning">عسكري: {{ match.excel_record.military_id }}</small>
                                    {% endif %}
                                </div>
                                <div class="col-md-6">
                                    <h6 class="text-primary mb-2">
                                        <i class="fas fa-database"></i> من قاعدة البيانات:
                                    </h6>
                                    <p class="mb-1"><strong>{{ match.db_name }}</strong></p>
                                    <small class="text-muted">ID: {{ match.db_record.id }}</small>
                                    {% if match.db_record.national_id %}
                                    <br><small class="badge bg-info">رقم وطني: {{ match.db_record.national_id }}</small>
                                    {% endif %}
                                    {% if match.db_record.phone %}
                                    <br><small class="badge bg-success">هاتف: {{ match.db_record.phone }}</small>
                                    {% endif %}
                                    {% if match.db_record.military_id %}
                                    <br><small class="badge bg-warning">عسكري: {{ match.db_record.military_id }}</small>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% if results.exact_matches|length > 10 %}
            <p class="text-muted text-center mt-3">
                <i class="fas fa-info-circle"></i>
                يتم عرض أول 10 مطابقات فقط. للاطلاع على جميع النتائج، قم بتصدير الملف.
            </p>
            {% endif %}
        </div>
    </div>
    {% endif %}

    <!-- New Records Section - غير موجود في قاعدة البيانات -->
    {% if results.new_records %}
    <div class="result-section">
        <div class="section-header">
            <h4 class="mb-0">
                <i class="fas fa-user-plus"></i> غير موجود في قاعدة البيانات - سيتم إدخال تقييم لهم
                <span class="badge bg-primary">{{ results.new_records|length }}</span>
            </h4>
        </div>
        <div class="p-3">
            <div class="alert alert-success">
                <i class="fas fa-user-plus"></i>
                <strong>إجراء مطلوب:</strong> هؤلاء الأشخاص غير موجودين في قاعدة البيانات. سيتم إضافتهم كأشخاص جدد مع تقييماتهم عند الاستيراد.
            </div>
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i>
                <strong>ملاحظة:</strong> سيتم إنشاء ملفات شخصية جديدة لهؤلاء الأشخاص وإدخال تقييماتهم في نفس الوقت.
            </div>
            {% for record in results.new_records[:20] %}
            <div class="card mb-2 border-primary">
                <div class="card-body p-2">
                    <div class="row">
                        <div class="col-md-6">
                            <strong>{{ record.corrected_name }}</strong>
                            <small class="text-muted d-block">الصف: {{ record.excel_record.row_index }}</small>
                            {% if record.excel_record.national_id %}
                            <small class="badge bg-info">رقم وطني: {{ record.excel_record.national_id }}</small>
                            {% endif %}
                            {% if record.excel_record.phone %}
                            <br><small class="badge bg-success">هاتف: {{ record.excel_record.phone }}</small>
                            {% endif %}
                            {% if record.excel_record.military_id %}
                            <br><small class="badge bg-warning">عسكري: {{ record.excel_record.military_id }}</small>
                            {% endif %}
                        </div>
                        <div class="col-md-3">
                            {% if record.excel_record.grade %}
                            <small class="text-muted">الدرجة:</small>
                            <br><strong class="text-primary">{{ record.excel_record.grade }}</strong>
                            {% endif %}
                            {% if record.excel_record.percentage %}
                            <br><small class="text-muted">النسبة:</small>
                            <br><strong class="text-success">{{ record.excel_record.percentage }}%</strong>
                            {% endif %}
                        </div>
                        <div class="col-md-3 text-end">
                            <span class="badge bg-success">
                                <i class="fas fa-plus"></i> سيتم إضافة تقييم
                            </span>
                            <br><small class="text-muted mt-1">شخص جديد + تقييم</small>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
            {% if results.new_records|length > 20 %}
            <p class="text-muted text-center mt-3">
                <i class="fas fa-info-circle"></i>
                يتم عرض أول 20 سجل فقط. للاطلاع على جميع النتائج، قم بتصدير الملف.
            </p>
            {% endif %}
        </div>
    </div>
    {% endif %}

            <!-- التقييمات المكررة -->
            {% if results.evaluation_analysis.duplicate_evaluations %}
            <div class="card mb-4 border-warning">
                <div class="card-header bg-warning text-dark">
                    <h4 class="mb-0">
                        <i class="fas fa-exclamation-triangle"></i>
                        التقييمات المكررة ({{ results.evaluation_analysis.duplicate_evaluations|length }})
                    </h4>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>الرقم</th>
                                    <th>الاسم من Excel</th>
                                    <th>الاسم الموجود</th>
                                    <th>تاريخ التقييم الموجود</th>
                                    <th>السبب</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for duplicate in results.evaluation_analysis.duplicate_evaluations %}
                                <tr>
                                    <td>{{ loop.index }}</td>
                                    <td>{{ duplicate.name }}</td>
                                    <td>{{ duplicate.existing_evaluation.name }}</td>
                                    <td>{{ duplicate.existing_evaluation.evaluation_date or '-' }}</td>
                                    <td><span class="badge bg-warning text-dark">{{ duplicate.reason }}</span></td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- الأسماء المصححة -->
            {% if results.corrected_names %}
            <div class="card mb-4 border-info">
                <div class="card-header bg-info text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-edit"></i>
                        الأسماء المصححة ({{ results.corrected_names|length }})
                    </h4>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>الرقم</th>
                                    <th>الاسم الأصلي</th>
                                    <th>الاسم المصحح</th>
                                    <th>رقم الصف في Excel</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for correction in results.corrected_names %}
                                <tr>
                                    <td>{{ loop.index }}</td>
                                    <td><span class="text-danger">{{ correction.original }}</span></td>
                                    <td><span class="text-success"><strong>{{ correction.corrected }}</strong></span></td>
                                    <td>{{ correction.row_index }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- السجلات المرفوضة -->
            {% if results.blocked_duplicates %}
            <div class="card mb-4 border-danger">
                <div class="card-header bg-danger text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-ban"></i>
                        السجلات المرفوضة ({{ results.blocked_duplicates|length }})
                    </h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-danger">
                        <i class="fas fa-info-circle"></i>
                        <strong>ملاحظة:</strong> هذه السجلات مرفوضة بسبب وجود تطابق في البيانات الشخصية مع سجلات موجودة في قاعدة البيانات
                    </div>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>الرقم</th>
                                    <th>الاسم</th>
                                    <th>الرقم الوطني</th>
                                    <th>رقم الهاتف</th>
                                    <th>السبب</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for blocked in results.blocked_duplicates %}
                                <tr>
                                    <td>{{ loop.index }}</td>
                                    <td>{{ blocked.corrected_name }}</td>
                                    <td>{{ blocked.excel_record.national_id or '-' }}</td>
                                    <td>{{ blocked.excel_record.phone or '-' }}</td>
                                    <td><span class="badge bg-danger">{{ blocked.reason }}</span></td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- الأسماء المكررة المسموحة -->
            {% if results.allowed_duplicates %}
            <div class="card mb-4 border-success">
                <div class="card-header bg-success text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-check-circle"></i>
                        التقييمات المكررة المسموحة ({{ results.allowed_duplicates|length }})
                    </h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-success">
                        <i class="fas fa-info-circle"></i>
                        <strong>ملاحظة:</strong> هذه الأسماء مكررة لكن البيانات الشخصية مختلفة، لذا يمكن إضافة تقييمات لها
                    </div>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>الرقم</th>
                                    <th>الاسم</th>
                                    <th>الرقم الوطني</th>
                                    <th>رقم الهاتف</th>
                                    <th>الرقم العسكري</th>
                                    <th>الدرجة</th>
                                    <th>السبب</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for allowed in results.allowed_duplicates %}
                                <tr>
                                    <td>{{ loop.index }}</td>
                                    <td><strong>{{ allowed.corrected_name }}</strong></td>
                                    <td>{{ allowed.excel_record.national_id or '-' }}</td>
                                    <td>{{ allowed.excel_record.phone or '-' }}</td>
                                    <td>{{ allowed.excel_record.military_id or '-' }}</td>
                                    <td>{{ allowed.excel_record.grade or '-' }}</td>
                                    <td><span class="badge bg-success">{{ allowed.reason }}</span></td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            {% endif %}

    <!-- Export Section -->
    <div class="mt-5 mb-4">
        <div class="card">
            <div class="card-body">
                <h5 class="text-danger text-center mb-4">
                    <i class="fas fa-star"></i> خيارات التصدير والاستيراد للتقييمات
                </h5>

                <div class="row">
                    <div class="col-md-4 mb-3">
                        <div class="card h-100 border-danger">
                            <div class="card-body text-center">
                                <i class="fas fa-download text-danger mb-3" style="font-size: 2rem;"></i>
                                <h6>التقرير الكامل للتقييمات</h6>
                                <p class="text-muted small">
                                    <strong>📊 محتوى التقرير:</strong><br>
                                    • <span class="text-success">✅ لديهم تقييم - سيتم التحديث:</span> {{ results.statistics.exact_matches_count }} شخص<br>
                                    • <span class="text-info">➕ ليس لديهم تقييم - سيتم الإضافة:</span> تفصيل كامل<br>
                                    • <span class="text-primary">🆕 غير موجود - سيتم إدخال تقييم لهم:</span> {{ results.statistics.new_records_count }} شخص<br>
                                    • <span class="text-warning">📝 أسماء مصححة:</span> {{ results.statistics.corrected_count }} اسم<br>
                                    • <span class="text-danger">❌ تقييمات مرفوضة:</span> {{ results.statistics.blocked_duplicates_count }} تقييم<br>
                                    • <span class="text-dark">📈 جميع الإحصائيات والتفاصيل</span> في أوراق منفصلة<br>
                                    <strong>📁 إجمالي:</strong> {{ results.statistics.total_processed }} سجل معالج
                                </p>
                                <a href="{{ url_for('person_data.export_analysis_results') }}" class="btn btn-danger">
                                    <i class="fas fa-download"></i> تحميل التقرير الكامل (Excel)
                                </a>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4 mb-3">
                        <div class="card h-100 border-success">
                            <div class="card-body text-center">
                                <i class="fas fa-file-excel text-success mb-3" style="font-size: 2rem;"></i>
                                <h6>التقييمات الجديدة فقط</h6>
                                <p class="text-muted small">
                                    الأشخاص الذين سيتم إدخال تقييمات لهم<br>
                                    (غير موجودين في قاعدة البيانات)
                                </p>
                                <a href="{{ url_for('person_data.export_new_names_only') }}" class="btn btn-success">
                                    <i class="fas fa-file-excel"></i> تحميل
                                </a>
                            </div>
                        </div>
                    </div>

                    {% if results.new_records or results.allowed_duplicates or results.corrected_names %}
                    <div class="col-md-4 mb-3">
                        <div class="card h-100 border-warning">
                            <div class="card-body text-center">
                                <i class="fas fa-database text-warning mb-3" style="font-size: 2rem;"></i>
                                <h6>إضافة التقييمات الجديدة</h6>
                                <p class="text-muted small">
                                    إضافة الأشخاص الجدد مع تقييماتهم<br>
                                    إلى قاعدة البيانات والدورة
                                </p>
                                <form method="post" action="{{ url_for('person_data.import_analyzed_names') }}">
                                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                    <button type="submit" class="btn btn-warning"
                                            onclick="return confirm('هل تريد إضافة {{ (results.new_records|length if results.new_records else 0) + (results.allowed_duplicates|length if results.allowed_duplicates else 0) + (results.corrected_names|length if results.corrected_names else 0) }} شخص جديد مع تقييماتهم إلى قاعدة البيانات{% if selected_course %} وإلى الدورة {{ selected_course.title }}{% endif %}؟')">
                                        <i class="fas fa-user-plus"></i> إضافة الأشخاص والتقييمات
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>

                <!-- إحصائيات التحليل -->
                <div class="mt-4 p-3 bg-light rounded">
                    <h6 class="text-danger mb-3 text-center">
                        <i class="fas fa-chart-pie"></i> ملخص تحليل التقييمات بالنسب المئوية
                    </h6>
                    <div class="row text-center">
                        <div class="col-md-3">
                            <div class="border-end">
                                <h5 class="text-success">{{ "%.1f"|format((results.statistics.new_records_count / results.statistics.total_processed * 100) if results.statistics.total_processed > 0 else 0) }}%</h5>
                                <small class="text-muted">سيتم إدخال تقييم لهم</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="border-end">
                                <h5 class="text-info">{{ "%.1f"|format((results.statistics.exact_matches_count / results.statistics.total_processed * 100) if results.statistics.total_processed > 0 else 0) }}%</h5>
                                <small class="text-muted">يحتاج تحديث تقييم</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="border-end">
                                <h5 class="text-warning">{{ "%.1f"|format((results.statistics.corrected_count / results.statistics.total_processed * 100) if results.statistics.total_processed > 0 else 0) }}%</h5>
                                <small class="text-muted">أسماء مصححة</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <h5 class="text-danger">{{ "%.1f"|format((results.statistics.blocked_duplicates_count / results.statistics.total_processed * 100) if results.statistics.total_processed > 0 else 0) }}%</h5>
                            <small class="text-muted">تقييمات مرفوضة</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Section -->
    <div class="row mt-5">
        <div class="col-12">
            <div id="evaluationChartsSection" class="card border-0 shadow-lg" style="background: linear-gradient(135deg, #ff9a56 0%, #ff6b6b 100%);">
                <div class="card-header text-white text-center border-0" style="background: transparent;">
                    <div class="d-flex justify-content-between align-items-center">
                        <div></div>
                        <div class="text-center">
                            <h3 class="mb-0 fw-bold">
                                <i class="fas fa-chart-pie me-2"></i> التقرير الكامل لتحليل التقييمات - الرسوم البيانية
                            </h3>
                            <p class="mb-0 mt-2 opacity-75">تحليل مرئي شامل لنتائج معالجة بيانات التقييمات</p>
                        </div>
                        <div class="dropdown evaluation-export-controls">
                            <button class="btn btn-light btn-sm dropdown-toggle" type="button" id="evaluationExportDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-download me-1"></i> تصدير الرسوم
                            </button>
                            <ul class="dropdown-menu" aria-labelledby="evaluationExportDropdown">
                                <li><a class="dropdown-item" href="#" onclick="exportEvaluationChartsToExcel()">
                                    <i class="fas fa-file-excel text-success me-2"></i> تصدير إلى Excel + صورة
                                </a></li>
                                <li><a class="dropdown-item" href="#" onclick="exportEvaluationChartsToWord()">
                                    <i class="fas fa-file-word text-primary me-2"></i> تصدير إلى Word
                                </a></li>
                                <li><a class="dropdown-item" href="#" onclick="exportEvaluationChartsAsPDF()">
                                    <i class="fas fa-file-pdf text-danger me-2"></i> تصدير إلى PDF
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="#" onclick="exportEvaluationChartsAsImages()">
                                    <i class="fas fa-images text-info me-2"></i> تصدير كصورة PNG
                                </a></li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="card-body" style="background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(10px);">

                    <!-- Main Statistics Chart -->
                    <div class="row mb-5">
                        <div class="col-lg-6 mb-4">
                            <div class="card h-100 border-0 shadow-sm">
                                <div class="card-header bg-gradient-primary text-white text-center">
                                    <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>توزيع التقييمات الرئيسية</h5>
                                </div>
                                <div class="card-body d-flex align-items-center justify-content-center" style="height: 400px;">
                                    <canvas id="evaluationMainStatsChart" style="max-width: 350px; max-height: 350px;"></canvas>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 mb-4">
                            <div class="card h-100 border-0 shadow-sm">
                                <div class="card-header bg-gradient-success text-white text-center">
                                    <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>إحصائيات التطابق</h5>
                                </div>
                                <div class="card-body d-flex align-items-center justify-content-center" style="height: 400px;">
                                    <canvas id="evaluationMatchChart" style="max-width: 350px; max-height: 350px;"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Progress Bars Section -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card border-0 shadow-sm">
                                <div class="card-header bg-gradient-info text-white text-center">
                                    <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>تقدم معالجة التقييمات بالنسب المئوية</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label class="fw-bold text-success mb-2">
                                                <i class="fas fa-check-circle me-1"></i>
                                                يحتاج تحديث تقييم ({{ results.statistics.exact_matches_count }})
                                            </label>
                                            {% set exact_percentage = (results.statistics.exact_matches_count / results.statistics.total_processed * 100) if results.statistics.total_processed > 0 else 0 %}
                                            <div class="progress mb-2" style="height: 25px;">
                                                <div class="progress-bar bg-success progress-bar-striped progress-bar-animated"
                                                     style="width: {{ exact_percentage }}%">
                                                    {{ "%.1f"|format(exact_percentage) }}%
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="fw-bold text-primary mb-2">
                                                <i class="fas fa-plus-circle me-1"></i>
                                                سيتم إدخال تقييم لهم ({{ results.statistics.new_records_count }})
                                            </label>
                                            {% set new_percentage = (results.statistics.new_records_count / results.statistics.total_processed * 100) if results.statistics.total_processed > 0 else 0 %}
                                            <div class="progress mb-2" style="height: 25px;">
                                                <div class="progress-bar bg-primary progress-bar-striped progress-bar-animated"
                                                     style="width: {{ new_percentage }}%">
                                                    {{ "%.1f"|format(new_percentage) }}%
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="fw-bold text-warning mb-2">
                                                <i class="fas fa-edit me-1"></i>
                                                أسماء مصححة ({{ results.statistics.corrected_count }})
                                            </label>
                                            {% set corrected_percentage = (results.statistics.corrected_count / results.statistics.total_processed * 100) if results.statistics.total_processed > 0 else 0 %}
                                            <div class="progress mb-2" style="height: 25px;">
                                                <div class="progress-bar bg-warning progress-bar-striped progress-bar-animated"
                                                     style="width: {{ corrected_percentage }}%">
                                                    {{ "%.1f"|format(corrected_percentage) }}%
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-md-6 mb-3">
                                            <label class="fw-bold text-danger mb-2">
                                                <i class="fas fa-ban me-1"></i>
                                                تقييمات مرفوضة ({{ results.statistics.blocked_duplicates_count }})
                                            </label>
                                            {% set blocked_eval_percentage = (results.statistics.blocked_duplicates_count / results.statistics.total_processed * 100) if results.statistics.total_processed > 0 else 0 %}
                                            <div class="progress mb-2" style="height: 25px;">
                                                <div class="progress-bar bg-danger progress-bar-striped progress-bar-animated"
                                                     style="width: {{ blocked_eval_percentage }}%">
                                                    {{ "%.1f"|format(blocked_eval_percentage) }}%
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Comparison Chart -->
                    <div class="row">
                        <div class="col-12">
                            <div class="card border-0 shadow-sm">
                                <div class="card-header bg-gradient-warning text-dark text-center">
                                    <h5 class="mb-0"><i class="fas fa-chart-area me-2"></i>مقارنة شاملة لنتائج التقييمات</h5>
                                </div>
                                <div class="card-body" style="height: 300px;">
                                    <canvas id="evaluationComparisonChart" style="max-height: 250px;"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>

</div>

<!-- Modal تحديث التقييمات -->
{% if results.exact_matches %}
<div class="modal fade" id="updateEvaluationsModal" tabindex="-1" aria-labelledby="updateEvaluationsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title" id="updateEvaluationsModalLabel">
                    <i class="fas fa-edit"></i> تحديث التقييمات المدخلة
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>ملاحظة:</strong> يمكنك تحديث بيانات التقييمات للأشخاص الموجودين في قاعدة البيانات.
                </div>

                <form method="post" action="{{ url_for('person_data.update_evaluations') }}">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">

                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>
                                        <input type="checkbox" id="selectAll" class="form-check-input">
                                    </th>
                                    <th>الاسم</th>
                                    <th>الرقم الوطني</th>
                                    <th>رقم الهاتف</th>
                                    <th>الرقم العسكري</th>
                                    <th>الإجراء</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for match in results.exact_matches %}
                                <tr>
                                    <td>
                                        <input type="checkbox" name="selected_evaluations" value="{{ match.db_record.id }}" class="form-check-input evaluation-checkbox">
                                    </td>
                                    <td><strong>{{ match.db_name }}</strong></td>
                                    <td>{{ match.db_record.national_id or '-' }}</td>
                                    <td>{{ match.db_record.phone or '-' }}</td>
                                    <td>{{ match.db_record.military_id or '-' }}</td>
                                    <td>
                                        <select name="action_{{ match.db_record.id }}" class="form-select form-select-sm">
                                            <option value="update_data">تحديث البيانات الشخصية</option>
                                            <option value="add_evaluation">إضافة تقييم جديد</option>
                                            <option value="update_evaluation">تحديث التقييم الحالي</option>
                                        </select>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <div class="text-center mt-3">
                        <button type="submit" class="btn btn-success btn-lg">
                            <i class="fas fa-save"></i> تحديث التقييمات المختارة
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // تأثيرات بصرية للبطاقات
        $('.stats-card, .result-section').hover(
            function() {
                $(this).addClass('shadow-lg');
            },
            function() {
                $(this).removeClass('shadow-lg');
            }
        );

        // إضافة تأثير للأزرار
        $('.btn-export, .btn-export-secondary').hover(
            function() {
                $(this).find('i').addClass('fa-bounce');
            },
            function() {
                $(this).find('i').removeClass('fa-bounce');
            }
        );

        // تحديد الكل في modal التحديث
        $('#selectAll').change(function() {
            $('.evaluation-checkbox').prop('checked', this.checked);
        });

        // تحديث حالة "تحديد الكل" عند تغيير الاختيارات الفردية
        $('.evaluation-checkbox').change(function() {
            var total = $('.evaluation-checkbox').length;
            var checked = $('.evaluation-checkbox:checked').length;
            $('#selectAll').prop('checked', total === checked);
        });
    });
</script>

<!-- Chart.js Library -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<!-- Export Libraries -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>

<script>
// تحضير بيانات التقييمات
const evalTotalProcessed = {{ results.statistics.total_processed }};
const evalExactMatches = {{ results.statistics.exact_matches_count }};
const evalNewRecords = {{ results.statistics.new_records_count }};
const evalCorrectedCount = {{ results.statistics.corrected_count }};
const evalBlockedDuplicates = {{ results.statistics.blocked_duplicates_count }};

// الألوان للتقييمات
const evalColors = {
    primary: '#007bff',
    success: '#28a745',
    warning: '#ffc107',
    danger: '#dc3545',
    info: '#17a2b8',
    purple: '#6f42c1',
    pink: '#e83e8c',
    orange: '#fd7e14'
};

// الرسم البياني الدائري الرئيسي للتقييمات
const evalMainStatsCtx = document.getElementById('evaluationMainStatsChart').getContext('2d');
new Chart(evalMainStatsCtx, {
    type: 'doughnut',
    data: {
        labels: ['يحتاج تحديث تقييم', 'سيتم إدخال تقييم لهم', 'أسماء مصححة', 'تقييمات مرفوضة'],
        datasets: [{
            data: [evalExactMatches, evalNewRecords, evalCorrectedCount, evalBlockedDuplicates],
            backgroundColor: [evalColors.success, evalColors.primary, evalColors.warning, evalColors.danger],
            borderWidth: 3,
            borderColor: '#fff',
            hoverBorderWidth: 5
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: true,
        aspectRatio: 1,
        plugins: {
            legend: {
                position: 'bottom',
                labels: {
                    padding: 15,
                    font: {
                        family: 'Cairo',
                        size: 11
                    }
                }
            },
            tooltip: {
                callbacks: {
                    label: function(context) {
                        const percentage = ((context.parsed / evalTotalProcessed) * 100).toFixed(1);
                        return context.label + ': ' + context.parsed + ' (' + percentage + '%)';
                    }
                }
            }
        }
    }
});

// رسم بياني للتطابق
const evalMatchCtx = document.getElementById('evaluationMatchChart').getContext('2d');
new Chart(evalMatchCtx, {
    type: 'bar',
    data: {
        labels: ['تطابق رقم وطني', 'تطابق رقم هاتف', 'تطابق رقم عسكري', 'تطابق كامل', 'تقييمات جديدة'],
        datasets: [{
            label: 'عدد الحالات',
            data: [
                {{ (results.statistics.get('name_national_id_matches', 0) + results.statistics.get('national_id_only_matches', 0)) if results.statistics.get('has_national_id_column', False) else 0 }},
                {{ (results.statistics.get('name_phone_matches', 0) + results.statistics.get('phone_only_matches', 0)) if results.statistics.get('has_phone_column', False) else 0 }},
                {{ (results.statistics.get('name_military_id_matches', 0) + results.statistics.get('military_id_only_matches', 0)) if results.statistics.get('has_military_id_column', False) else 0 }},
                evalExactMatches,
                evalNewRecords
            ],
            backgroundColor: [evalColors.info, evalColors.purple, evalColors.pink, evalColors.success, evalColors.primary],
            borderColor: [evalColors.info, evalColors.purple, evalColors.pink, evalColors.success, evalColors.primary],
            borderWidth: 2,
            borderRadius: 5
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: true,
        aspectRatio: 1,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    stepSize: 1
                }
            }
        }
    }
});

// رسم بياني للمقارنة
const evalComparisonCtx = document.getElementById('evaluationComparisonChart').getContext('2d');
new Chart(evalComparisonCtx, {
    type: 'line',
    data: {
        labels: ['إجمالي المعالجة', 'يحتاج تحديث تقييم', 'سيتم إدخال تقييم لهم', 'أسماء مصححة', 'تقييمات مرفوضة'],
        datasets: [{
            label: 'العدد',
            data: [evalTotalProcessed, evalExactMatches, evalNewRecords, evalCorrectedCount, evalBlockedDuplicates],
            borderColor: evalColors.primary,
            backgroundColor: evalColors.primary + '20',
            borderWidth: 3,
            fill: true,
            tension: 0.4,
            pointBackgroundColor: evalColors.primary,
            pointBorderColor: '#fff',
            pointBorderWidth: 2,
            pointRadius: 6,
            pointHoverRadius: 8
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: true,
        aspectRatio: 3,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    stepSize: 1
                }
            }
        }
    }
});

// دوال التصدير للتقييمات
function exportEvaluationChartsToExcel() {
    captureEvaluationChartsSection().then(canvas => {
        // تحويل الصورة إلى base64
        const imageData = canvas.toDataURL('image/png');

        // تحميل الصورة مباشرة
        downloadImage(imageData, 'تقرير_الرسوم_البيانية_التقييمات_' + new Date().toISOString().split('T')[0] + '.png');

        // إنشاء workbook مع البيانات
        const wb = XLSX.utils.book_new();

        // بيانات الإحصائيات
        const data = [
            ['تقرير تحليل التقييمات - الرسوم البيانية'],
            ['تاريخ التقرير: ' + new Date().toLocaleDateString('ar-SA')],
            [''],
            ['الإحصائيات الرئيسية:'],
            ['نوع التقييم', 'العدد', 'النسبة المئوية'],
            ['يحتاج تحديث تقييم', evalExactMatches, ((evalExactMatches / evalTotalProcessed) * 100).toFixed(1) + '%'],
            ['سيتم إدخال تقييم لهم', evalNewRecords, ((evalNewRecords / evalTotalProcessed) * 100).toFixed(1) + '%'],
            ['أسماء مصححة', evalCorrectedCount, ((evalCorrectedCount / evalTotalProcessed) * 100).toFixed(1) + '%'],
            ['تقييمات مرفوضة', evalBlockedDuplicates, ((evalBlockedDuplicates / evalTotalProcessed) * 100).toFixed(1) + '%'],
            ['إجمالي المعالجة', evalTotalProcessed, '100%'],
            [''],
            ['إحصائيات التطابق:'],
            ['نوع التطابق', 'العدد'],
            ['تطابق رقم وطني', {{ (results.statistics.get('name_national_id_matches', 0) + results.statistics.get('national_id_only_matches', 0)) if results.statistics.get('has_national_id_column', False) else 0 }}],
            ['تطابق رقم هاتف', {{ (results.statistics.get('name_phone_matches', 0) + results.statistics.get('phone_only_matches', 0)) if results.statistics.get('has_phone_column', False) else 0 }}],
            ['تطابق رقم عسكري', {{ (results.statistics.get('name_military_id_matches', 0) + results.statistics.get('military_id_only_matches', 0)) if results.statistics.get('has_military_id_column', False) else 0 }}],
            ['تطابق كامل', evalExactMatches],
            ['تقييمات جديدة', evalNewRecords],
            [''],
            ['ملاحظة: تم تحميل الرسوم البيانية كصورة منفصلة']
        ];

        const ws = XLSX.utils.aoa_to_sheet(data);

        // تنسيق الأعمدة
        ws['!cols'] = [{wch: 30}, {wch: 15}, {wch: 20}];

        XLSX.utils.book_append_sheet(wb, ws, 'تقرير الرسوم البيانية');

        // تصدير Excel
        XLSX.writeFile(wb, 'تقرير_الرسوم_البيانية_التقييمات_بيانات_' + new Date().toISOString().split('T')[0] + '.xlsx');

        showEvaluationExportSuccess('Excel + صورة PNG');
    });
}

function exportEvaluationChartsToWord() {
    captureEvaluationChartsSection().then(canvas => {
        const imageData = canvas.toDataURL('image/png');

        // إنشاء محتوى HTML مع الصورة
        let htmlContent = `
            <html dir="rtl">
            <head>
                <meta charset="UTF-8">
                <style>
                    body { font-family: 'Cairo', Arial, sans-serif; direction: rtl; text-align: center; }
                    .header { color: #ff6b6b; margin-bottom: 30px; }
                    .chart-image { max-width: 100%; height: auto; border: 1px solid #ddd; margin: 20px 0; }
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>تقرير تحليل التقييمات - الرسوم البيانية</h1>
                    <p>تاريخ التقرير: ${new Date().toLocaleDateString('ar-SA')}</p>
                </div>

                <div>
                    <img src="${imageData}" class="chart-image" alt="الرسوم البيانية للتقييمات">
                </div>
            </body>
            </html>
        `;

        // إنشاء blob وتحميله
        const blob = new Blob([htmlContent], { type: 'application/msword' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'تقرير_الرسوم_البيانية_التقييمات_' + new Date().toISOString().split('T')[0] + '.doc';
        a.click();
        URL.revokeObjectURL(url);

        showEvaluationExportSuccess('Word');
    });
}

function exportEvaluationChartsAsPDF() {
    captureEvaluationChartsSection().then(canvas => {
        const { jsPDF } = window.jspdf;
        const pdf = new jsPDF('l', 'mm', 'a4'); // landscape للحصول على مساحة أكبر

        // إضافة العنوان
        pdf.setFontSize(16);
        pdf.text('تقرير تحليل التقييمات - الرسوم البيانية', 148, 20, { align: 'center' });

        // إضافة التاريخ
        pdf.setFontSize(12);
        pdf.text('تاريخ التقرير: ' + new Date().toLocaleDateString('ar-SA'), 148, 30, { align: 'center' });

        // إضافة الصورة
        const imgData = canvas.toDataURL('image/png');
        const imgWidth = 250;
        const imgHeight = (canvas.height * imgWidth) / canvas.width;

        // التأكد من أن الصورة تدخل في الصفحة
        const maxHeight = 180;
        let finalWidth = imgWidth;
        let finalHeight = imgHeight;

        if (imgHeight > maxHeight) {
            finalHeight = maxHeight;
            finalWidth = (canvas.width * maxHeight) / canvas.height;
        }

        const x = (297 - finalWidth) / 2;
        const y = 40;

        pdf.addImage(imgData, 'PNG', x, y, finalWidth, finalHeight);

        // حفظ الملف
        pdf.save('تقرير_الرسوم_البيانية_التقييمات_' + new Date().toISOString().split('T')[0] + '.pdf');

        showEvaluationExportSuccess('PDF');
    });
}

function exportEvaluationChartsAsImages() {
    // تصدير القسم كاملاً كصورة واحدة
    captureEvaluationChartsSection().then(canvas => {
        const imageData = canvas.toDataURL('image/png');
        downloadImage(imageData, 'تقرير_الرسوم_البيانية_التقييمات_كامل_' + new Date().toISOString().split('T')[0] + '.png');
        showEvaluationExportSuccess('صورة كاملة للتقرير');
    });
}

// دالة التقاط قسم الرسوم البيانية للتقييمات كاملاً
function captureEvaluationChartsSection() {
    const element = document.getElementById('evaluationChartsSection');
    const exportControls = element.querySelector('.evaluation-export-controls');

    // إخفاء أدوات التصدير مؤقتاً
    if (exportControls) {
        exportControls.style.visibility = 'hidden';
    }

    // انتظار قصير للتأكد من إخفاء العناصر
    return new Promise(resolve => {
        setTimeout(() => {
            html2canvas(element, {
                useCORS: true,
                allowTaint: true,
                scale: 2, // جودة عالية
                backgroundColor: null, // الحفاظ على الخلفية الأصلية
                width: element.scrollWidth,
                height: element.scrollHeight,
                scrollX: 0,
                scrollY: 0,
                ignoreElements: function(el) {
                    // تجاهل عناصر التصدير
                    return el.classList.contains('evaluation-export-controls') ||
                           el.classList.contains('dropdown') ||
                           el.classList.contains('dropdown-menu') ||
                           el.id === 'evaluationExportDropdown';
                }
            }).then(canvas => {
                // إعادة إظهار أدوات التصدير
                if (exportControls) {
                    exportControls.style.visibility = 'visible';
                }
                resolve(canvas);
            });
        }, 100);
    });
}

function showEvaluationExportSuccess(format) {
    // إنشاء تنبيه نجاح
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-success alert-dismissible fade show position-fixed';
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        <i class="fas fa-check-circle me-2"></i>
        <strong>تم التصدير بنجاح!</strong><br>
        تم تصدير تقرير التقييمات إلى ${format}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(alertDiv);

    // إزالة التنبيه بعد 5 ثوان
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.parentNode.removeChild(alertDiv);
        }
    }, 5000);
}
</script>
{% endblock %}
