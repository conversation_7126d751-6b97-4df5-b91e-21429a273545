/* ملف CSS موحد خاص برأس الصفحة فقط */
/* يطبق على جميع الصفحات بنفس التصميم */

/* إعادة تعيين شاملة لرأس الصفحة */
header, .modern-header, nav, .navbar {
    margin: 0 !important;
    padding: 0 !important;
    border: none !important;
    background: none !important;
}

/* الشريط العلوي الموحد */
.top-bar {
    background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 50%, #60a5fa 100%) !important;
    color: white !important;
    padding: 8px 0 !important;
    font-size: 0.9rem !important;
    box-shadow: 0 2px 10px rgba(30, 58, 138, 0.3) !important;
    width: 100% !important;
    position: relative !important;
    z-index: 1001 !important;
}

.header-info, .header-contact {
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
    color: white !important;
}

.header-info i, .header-contact i {
    color: #e0f2fe !important;
    font-size: 1rem !important;
}

.header-info i.fa-clock {
    color: #81d4fa !important;
}

.header-info i.fa-calendar {
    color: #a5d6a7 !important;
}

.header-contact i.fa-phone {
    color: #ffcc80 !important;
}

.separator {
    margin: 0 15px !important;
    opacity: 0.7 !important;
    color: white !important;
}

#current-time, #current-date {
    font-weight: 600 !important;
    color: #ffffff !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
}

/* الشريط الرئيسي الموحد */
.main-navbar, .navbar {
    background: white !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1) !important;
    padding: 15px 0 !important;
    border-bottom: 3px solid #f8f9fa !important;
    width: 100% !important;
    position: relative !important;
    z-index: 1000 !important;
}

/* الشعار الموحد */
.enhanced-brand, .navbar-brand {
    text-decoration: none !important;
    color: inherit !important;
    display: flex !important;
    align-items: center !important;
}

.brand-container {
    display: flex !important;
    align-items: center !important;
    gap: 15px !important;
}

.brand-icon {
    width: 50px !important;
    height: 50px !important;
    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%) !important;
    border-radius: 12px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    color: white !important;
    font-size: 1.5rem !important;
    box-shadow: 0 4px 15px rgba(30, 64, 175, 0.3) !important;
}

.brand-icon i, .brand-icon .fas {
    color: white !important;
    font-size: 1.5rem !important;
}

.brand-text {
    display: flex !important;
    flex-direction: column !important;
}

.brand-title {
    font-size: 1.4rem !important;
    font-weight: 800 !important;
    color: #1e40af !important;
    line-height: 1.2 !important;
    font-family: 'Cairo', 'Tajawal', sans-serif !important;
    margin: 0 !important;
}

.brand-subtitle {
    font-size: 0.85rem !important;
    color: #6b7280 !important;
    font-weight: 500 !important;
    margin: 0 !important;
}

/* الروابط الموحدة */
.enhanced-link, .nav-link {
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
    padding: 12px 20px !important;
    border-radius: 10px !important;
    transition: all 0.3s ease !important;
    color: #374151 !important;
    font-weight: 600 !important;
    margin: 0 5px !important;
    text-decoration: none !important;
    background: transparent !important;
}

.enhanced-link:hover, .nav-link:hover {
    background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%) !important;
    color: white !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 5px 15px rgba(59, 130, 246, 0.4) !important;
    text-decoration: none !important;
}

.enhanced-link i, .nav-link i {
    font-size: 1.1rem !important;
}

/* زر التبديل الموحد */
.custom-toggler, .navbar-toggler {
    border: none !important;
    padding: 8px !important;
    background: #f3f4f6 !important;
    border-radius: 8px !important;
    width: 45px !important;
    height: 45px !important;
    display: flex !important;
    flex-direction: column !important;
    justify-content: center !important;
    align-items: center !important;
    gap: 4px !important;
}

.custom-toggler span, .navbar-toggler span {
    display: block !important;
    width: 25px !important;
    height: 3px !important;
    background: #374151 !important;
    border-radius: 2px !important;
    transition: all 0.3s ease !important;
}

.custom-toggler:hover span, .navbar-toggler:hover span {
    background: #1e40af !important;
}

/* قائمة المستخدم الموحدة */
.user-dropdown .user-link {
    display: flex !important;
    align-items: center !important;
    gap: 10px !important;
    padding: 8px 15px !important;
    border-radius: 25px !important;
    background: #f8fafc !important;
    border: 2px solid #e2e8f0 !important;
    transition: all 0.3s ease !important;
    color: #1e40af !important;
    text-decoration: none !important;
}

.user-dropdown .user-link:hover {
    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%) !important;
    color: white !important;
    border-color: #1e40af !important;
    box-shadow: 0 4px 15px rgba(30, 64, 175, 0.3) !important;
}

.user-avatar {
    width: 35px !important;
    height: 35px !important;
    background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%) !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    color: white !important;
    font-size: 1rem !important;
}

.user-name {
    font-weight: 600 !important;
    font-size: 0.95rem !important;
}

/* القائمة المنسدلة الموحدة */
.enhanced-dropdown, .dropdown-menu {
    background: white !important;
    border: none !important;
    border-radius: 15px !important;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15) !important;
    padding: 10px 0 !important;
    margin-top: 10px !important;
    min-width: 220px !important;
}

.enhanced-dropdown .dropdown-header, .dropdown-header {
    padding: 15px 20px !important;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%) !important;
    color: #1e40af !important;
    font-weight: 700 !important;
    border-radius: 10px !important;
    margin: 5px 10px 10px 10px !important;
}

.enhanced-dropdown .dropdown-item, .dropdown-item {
    padding: 12px 20px !important;
    color: #374151 !important;
    font-weight: 500 !important;
    transition: all 0.3s ease !important;
    display: flex !important;
    align-items: center !important;
    gap: 10px !important;
    margin: 2px 10px !important;
    border-radius: 8px !important;
    text-decoration: none !important;
}

.enhanced-dropdown .dropdown-item:hover, .dropdown-item:hover {
    background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%) !important;
    color: white !important;
    transform: translateX(5px) !important;
    text-decoration: none !important;
}

.enhanced-dropdown .logout-link:hover, .logout-link:hover {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%) !important;
}

/* زر تسجيل الدخول الموحد */
.login-btn {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%) !important;
    color: white !important;
    padding: 10px 20px !important;
    border-radius: 25px !important;
    font-weight: 600 !important;
    transition: all 0.3s ease !important;
    text-decoration: none !important;
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
}

.login-btn:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 5px 15px rgba(16, 185, 129, 0.4) !important;
    color: white !important;
    text-decoration: none !important;
}

/* شريط التنقل السريع الموحد */
.quick-nav {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%) !important;
    padding: 12px 0 !important;
    border-top: 1px solid #e5e7eb !important;
    width: 100% !important;
}

.quick-links {
    display: flex !important;
    gap: 20px !important;
    justify-content: center !important;
    flex-wrap: wrap !important;
}

.quick-link {
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
    padding: 8px 16px !important;
    background: white !important;
    color: #374151 !important;
    text-decoration: none !important;
    border-radius: 20px !important;
    font-size: 0.9rem !important;
    font-weight: 500 !important;
    transition: all 0.3s ease !important;
    border: 1px solid #e5e7eb !important;
}

.quick-link:hover {
    background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%) !important;
    color: white !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3) !important;
    text-decoration: none !important;
}

.quick-link i {
    font-size: 1rem !important;
}

/* الحاوي الرئيسي لرأس الصفحة */
.modern-header {
    position: sticky !important;
    top: 0 !important;
    z-index: 1000 !important;
    background: white !important;
    width: 100% !important;
    display: block !important;
    visibility: visible !important;
}

/* تحسينات الاستجابة */
@media (max-width: 768px) {
    .top-bar {
        text-align: center !important;
    }
    
    .header-info, .header-contact {
        justify-content: center !important;
        margin: 5px 0 !important;
    }
    
    .brand-container {
        gap: 10px !important;
    }
    
    .brand-icon {
        width: 40px !important;
        height: 40px !important;
        font-size: 1.2rem !important;
    }
    
    .brand-title {
        font-size: 1.2rem !important;
    }
    
    .brand-subtitle {
        font-size: 0.8rem !important;
    }
    
    .quick-links {
        gap: 10px !important;
    }
    
    .quick-link {
        font-size: 0.8rem !important;
        padding: 6px 12px !important;
    }
}
