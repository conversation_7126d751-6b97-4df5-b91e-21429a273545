/* الخط العربي المحسن */
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;800;900&family=Cairo:wght@300;400;500;600;700;800;900&display=swap');

/* تحسينات الأداء والتمرير السلس */
* {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
    overflow-scrolling: touch;
}

/* تحسين الأداء للعناصر المتحركة */
*[class*="animation"],
*[class*="animate"],
.card:hover,
.btn:hover {
    will-change: transform;
    transform: translateZ(0);
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
}

/* تحسينات خاصة للتمرير السلس */
body, html {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

/* تقليل إعادة الرسم أثناء التمرير */
.container, .container-fluid {
    will-change: auto;
    transform: translateZ(0);
}

/* تحسين الأداء للعناصر الثابتة */
.navbar, .sidebar {
    will-change: auto;
    transform: translateZ(0);
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
}

body {
    font-family: 'Cairo', 'Tajawal', sans-serif;
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 25%, #90caf9 50%, #64b5f6 75%, #42a5f5 100%);
    min-height: 100vh;
    line-height: 1.6;
    position: relative;
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(25, 118, 210, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(66, 165, 245, 0.1) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}

/* تنسيق العناوين */
h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
}

/* تنسيق الأزرار المحسن */
.btn {
    border-radius: 12px;
    padding: 12px 24px;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    /* تحسينات الأداء */
    will-change: transform;
    transform: translateZ(0);
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
}

.btn-primary {
    background: linear-gradient(135deg, #1976d2 0%, #42a5f5 100%);
    border: none;
    box-shadow: 0 8px 25px rgba(25, 118, 210, 0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #1565c0 0%, #1976d2 100%);
    transform: translateY(-2px) translateZ(0);
    box-shadow: 0 8px 25px rgba(25, 118, 210, 0.3);
}

/* تنسيق البطاقات المحسن */
.card {
    border-radius: 20px;
    overflow: hidden;
    margin-bottom: 25px;
    box-shadow: 0 15px 35px rgba(25, 118, 210, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 1);
    /* تحسينات الأداء */
    will-change: transform;
    transform: translateZ(0);
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
}

.card:hover {
    transform: translateY(-5px) translateZ(0);
    box-shadow: 0 20px 40px rgba(25, 118, 210, 0.15);
}

/* تنسيق شريط التنقل المحسن */
.navbar {
    box-shadow: 0 8px 32px rgba(25, 118, 210, 0.2);
    backdrop-filter: blur(15px);
    background: linear-gradient(135deg, rgba(25, 118, 210, 0.95) 0%, rgba(66, 165, 245, 0.95) 100%) !important;
    border-radius: 0 0 25px 25px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.navbar-brand {
    font-weight: 800;
    font-size: 1.6rem;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    filter: drop-shadow(0 2px 4px rgba(255,255,255,0.1));
}

/* تنسيق التذييل المحسن */
footer {
    margin-top: 50px;
    padding: 30px 0;
    background: linear-gradient(135deg, #1565c0 0%, #1976d2 50%, #1e88e5 100%);
    color: white;
    border-radius: 25px 25px 0 0;
    box-shadow: 0 -8px 32px rgba(25, 118, 210, 0.2);
    backdrop-filter: blur(15px);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* تنسيق النماذج المحسن */
.form-control {
    border-radius: 12px;
    padding: 15px 20px;
    margin-bottom: 20px;
    border: 2px solid rgba(25, 118, 210, 0.2);
    background: rgba(255, 255, 255, 1);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #1976d2;
    box-shadow: 0 0 0 0.25rem rgba(25, 118, 210, 0.25);
    background: rgba(255, 255, 255, 1);
    transform: translateY(-2px);
}

/* تنسيق الرسائل التنبيهية المحسن */
.alert {
    border-radius: 15px;
    border: none;
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 25px rgba(25, 118, 210, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.2);
    font-weight: 500;
}

.alert-success {
    background: linear-gradient(135deg, rgba(76, 175, 80, 0.9) 0%, rgba(129, 199, 132, 0.9) 100%);
    color: white;
}

.alert-info {
    background: linear-gradient(135deg, rgba(25, 118, 210, 0.9) 0%, rgba(66, 165, 245, 0.9) 100%);
    color: white;
}

.alert-warning {
    background: linear-gradient(135deg, rgba(255, 152, 0, 0.9) 0%, rgba(255, 183, 77, 0.9) 100%);
    color: white;
}

.alert-danger {
    background: linear-gradient(135deg, rgba(244, 67, 54, 0.9) 0%, rgba(239, 83, 80, 0.9) 100%);
    color: white;
}

/* تنسيق الصور */
.img-thumbnail {
    border-radius: 10px;
}

/* تنسيق الروابط المحسن */
a {
    color: #1976d2;
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
}

a:hover {
    color: #1565c0;
    text-decoration: none;
    transform: translateY(-1px);
}

a:not(.btn):not(.nav-link):not(.sidebar-link):hover::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(135deg, #1976d2 0%, #42a5f5 100%);
    border-radius: 1px;
}

/* تنسيق القوائم */
.list-group-item {
    border-left: none;
    border-right: none;
    padding: 15px;
}

/* تنسيق الأيقونات المحسن */
.icon-circle {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    color: #1976d2;
    margin-bottom: 20px;
    box-shadow: 0 8px 25px rgba(25, 118, 210, 0.2);
    transition: all 0.3s ease;
}

.icon-circle:hover {
    transform: translateY(-3px) scale(1.1);
    box-shadow: 0 12px 35px rgba(25, 118, 210, 0.3);
}

/* تنسيق الشارات المحسن */
.badge {
    padding: 8px 16px;
    border-radius: 25px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.badge.bg-primary {
    background: linear-gradient(135deg, #1976d2 0%, #42a5f5 100%) !important;
}

/* تنسيق الجداول المحسن */
.table {
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 15px 35px rgba(25, 118, 210, 0.1);
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.table thead {
    background: linear-gradient(135deg, #1976d2 0%, #42a5f5 100%);
    color: white;
}

.table tbody tr:hover {
    background: rgba(25, 118, 210, 0.05);
    transform: translateZ(0);
    transition: background-color 0.2s ease;
}

/* تنسيق الصفحة الرئيسية */
.hero-section {
    background: linear-gradient(135deg, #4a6bff 0%, #2541b2 100%);
    color: white;
    padding: 100px 0;
    margin-bottom: 50px;
    border-radius: 0 0 50px 50px;
}

.feature-icon {
    font-size: 3rem;
    color: #4a6bff;
    margin-bottom: 20px;
}

/* تنسيق لوحة التحكم */
.dashboard-stats {
    display: flex;
    justify-content: space-between;
    margin-bottom: 30px;
}

.stat-item {
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    flex: 1;
    margin: 0 10px;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: #4a6bff;
    margin: 10px 0;
}

/* تنسيق الصفحات الفرعية */
.page-header {
    background-color: #f8f9fa;
    padding: 30px 0;
    margin-bottom: 30px;
    border-radius: 10px;
}

.page-title {
    margin: 0;
    font-size: 2rem;
    color: #2541b2;
}

/* تنسيق الأقسام */
.section {
    margin-bottom: 50px;
}

.section-title {
    margin-bottom: 20px;
    position: relative;
    padding-bottom: 10px;
}

.section-title:after {
    content: '';
    position: absolute;
    bottom: 0;
    right: 0;
    width: 50px;
    height: 3px;
    background-color: #4a6bff;
}
